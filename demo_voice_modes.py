#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LiveTalking 双语音模式演示脚本
"""

import time
import webbrowser
import subprocess
import sys
import os
import requests
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🎤 LiveTalking 双语音交互模式演示")
    print("=" * 60)
    print()

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    # 检查 Python 版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 版本过低，需要 Python 3.8+")
        return False
    print(f"✅ Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要文件
    required_files = [
        "app.py",
        "web/dashboard.html",
        "web/test_voice_modes.html",
        "llm.py",
        "ttsreal.py"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少文件: {file_path}")
            return False
        print(f"✅ 文件存在: {file_path}")
    
    return True

def check_services():
    """检查服务状态"""
    print("\n🔍 检查服务状态...")
    
    services = {
        "Ollama": "http://localhost:11434/api/tags",
        "GPT-SoVITS": "http://127.0.0.1:9880/"
    }
    
    service_status = {}
    
    for service_name, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {service_name} 服务正常")
                service_status[service_name] = True
            else:
                print(f"❌ {service_name} 服务异常 (状态码: {response.status_code})")
                service_status[service_name] = False
        except requests.exceptions.RequestException as e:
            print(f"❌ {service_name} 服务不可达: {e}")
            service_status[service_name] = False
    
    return service_status

def start_livetalking():
    """启动 LiveTalking 应用"""
    print("\n🚀 启动 LiveTalking 应用...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['HTTP_PROXY'] = ''
    env['HTTPS_PROXY'] = ''
    env['NO_PROXY'] = '127.0.0.1,localhost,*.local'
    env['PYTHONIOENCODING'] = 'utf-8'
    
    # 启动命令
    cmd = [
        sys.executable, "app.py",
        "--transport", "webrtc",
        "--model", "wav2lip",
        "--avatar_id", "wav2lip256_avatar1",
        "--tts", "gpt-sovits",
        "--TTS_SERVER", "http://127.0.0.1:9880",
        "--REF_FILE", "D:\\ai\\Liv2\\ref.wav",
        "--REF_TEXT", "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 启动应用（非阻塞）
        process = subprocess.Popen(cmd, env=env)
        print("✅ LiveTalking 应用已启动")
        return process
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

def wait_for_service(url, timeout=30):
    """等待服务启动"""
    print(f"⏳ 等待服务启动: {url}")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print("✅ 服务已启动")
                return True
        except:
            pass
        time.sleep(1)
        print(".", end="", flush=True)
    
    print("\n❌ 服务启动超时")
    return False

def open_demo_pages():
    """打开演示页面"""
    print("\n🌐 打开演示页面...")
    
    pages = [
        ("主界面", "http://127.0.0.1:8010/dashboard.html"),
        ("测试页面", "http://127.0.0.1:8010/test_voice_modes.html")
    ]
    
    for name, url in pages:
        print(f"📖 打开 {name}: {url}")
        try:
            webbrowser.open(url)
            time.sleep(1)  # 避免同时打开太多页面
        except Exception as e:
            print(f"❌ 无法打开 {name}: {e}")

def show_usage_guide():
    """显示使用指南"""
    print("\n📚 使用指南:")
    print("-" * 40)
    print("1. 🎯 主界面功能:")
    print("   • 点击'开始连接'建立 WebRTC 连接")
    print("   • 在对话模式中选择语音交互方式")
    print()
    print("2. 🎤 按住说话模式:")
    print("   • 按住麦克风按钮说话")
    print("   • 松开按钮自动发送")
    print("   • 适合短句交流")
    print()
    print("3. 🔄 持续对话模式:")
    print("   • 点击'开始持续对话'")
    print("   • 系统持续监听语音")
    print("   • 自动识别并发送")
    print("   • 点击'停止'结束对话")
    print()
    print("4. 🧪 测试页面功能:")
    print("   • 检查浏览器功能支持")
    print("   • 测试语音识别功能")
    print("   • 测试麦克风访问")
    print("   • 验证服务连接")
    print()
    print("5. 💡 使用建议:")
    print("   • 首次使用请允许麦克风权限")
    print("   • 在安静环境中使用效果更佳")
    print("   • 可随时在两种模式间切换")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请解决上述问题后重试")
        return
    
    # 检查服务
    service_status = check_services()
    
    # 检查关键服务
    if not service_status.get("Ollama", False):
        print("\n⚠️  Ollama 服务未运行，LLM 功能可能不可用")
        print("请运行: ollama serve")
    
    if not service_status.get("GPT-SoVITS", False):
        print("\n⚠️  GPT-SoVITS 服务未运行，TTS 功能可能不可用")
        print("请确保 GPT-SoVITS 在 http://127.0.0.1:9880 运行")
    
    # 询问是否继续
    print("\n" + "=" * 60)
    continue_demo = input("是否继续启动演示？(y/n): ").lower().strip()
    if continue_demo != 'y':
        print("演示已取消")
        return
    
    # 启动应用
    process = start_livetalking()
    if not process:
        print("❌ 无法启动应用")
        return
    
    try:
        # 等待服务启动
        if wait_for_service("http://127.0.0.1:8010/dashboard.html"):
            # 打开演示页面
            open_demo_pages()
            
            # 显示使用指南
            show_usage_guide()
            
            print("\n" + "=" * 60)
            print("🎉 演示环境已准备就绪！")
            print("📱 请在浏览器中体验双语音交互模式")
            print("⌨️  按 Ctrl+C 停止演示")
            print("=" * 60)
            
            # 等待用户中断
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n\n🛑 用户中断演示")
        else:
            print("❌ 服务启动失败")
    
    finally:
        # 清理进程
        if process and process.poll() is None:
            print("🧹 清理进程...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            print("✅ 进程已清理")

if __name__ == "__main__":
    main()
