#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LiveTalking 实时语音转文字显示功能演示脚本
"""

import time
import webbrowser
import subprocess
import sys
import os
import requests
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("🎤 LiveTalking 实时语音转文字显示功能演示")
    print("=" * 70)
    print()

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    # 检查必要文件
    required_files = [
        "web/dashboard.html",
        "web/test_speech_display.html",
        "web/test_voice_modes.html"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少文件: {file_path}")
            return False
        print(f"✅ 文件存在: {file_path}")
    
    return True

def start_livetalking():
    """启动 LiveTalking 应用"""
    print("\n🚀 启动 LiveTalking 应用...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['HTTP_PROXY'] = ''
    env['HTTPS_PROXY'] = ''
    env['NO_PROXY'] = '127.0.0.1,localhost,*.local'
    env['PYTHONIOENCODING'] = 'utf-8'
    
    # 启动命令
    cmd = [
        sys.executable, "app.py",
        "--transport", "webrtc",
        "--model", "wav2lip",
        "--avatar_id", "wav2lip256_avatar1",
        "--tts", "gpt-sovits",
        "--TTS_SERVER", "http://127.0.0.1:9880",
        "--REF_FILE", "D:\\ai\\Liv2\\ref.wav",
        "--REF_TEXT", "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"
    ]
    
    try:
        process = subprocess.Popen(cmd, env=env)
        print("✅ LiveTalking 应用已启动")
        return process
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

def wait_for_service(url, timeout=30):
    """等待服务启动"""
    print(f"⏳ 等待服务启动: {url}")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print("✅ 服务已启动")
                return True
        except:
            pass
        time.sleep(1)
        print(".", end="", flush=True)
    
    print("\n❌ 服务启动超时")
    return False

def open_demo_pages():
    """打开演示页面"""
    print("\n🌐 打开演示页面...")
    
    pages = [
        ("主界面 (实时语音显示)", "http://127.0.0.1:8010/dashboard.html"),
        ("实时显示测试页面", "http://127.0.0.1:8010/test_speech_display.html"),
        ("语音模式测试页面", "http://127.0.0.1:8010/test_voice_modes.html")
    ]
    
    for name, url in pages:
        print(f"📖 打开 {name}: {url}")
        try:
            webbrowser.open(url)
            time.sleep(1)
        except Exception as e:
            print(f"❌ 无法打开 {name}: {e}")

def show_feature_guide():
    """显示功能指南"""
    print("\n📚 实时语音转文字显示功能指南:")
    print("-" * 50)
    print("🎯 新功能特性:")
    print("   • 实时显示语音识别进度")
    print("   • 区分临时结果和最终结果")
    print("   • 语音指示器和状态预览")
    print("   • 支持手动编辑识别结果")
    print()
    print("🎨 视觉效果:")
    print("   • 灰色斜体 = 临时识别结果（识别中）")
    print("   • 蓝色加粗 = 最终识别结果（已确认）")
    print("   • 红色脉冲 = 语音识别指示器")
    print("   • 预览区域 = 详细状态信息")
    print()
    print("🎤 按住说话模式:")
    print("   1. 选择'按住说话'标签")
    print("   2. 按住麦克风按钮开始说话")
    print("   3. 观察文本框中的实时识别结果")
    print("   4. 松开按钮查看最终结果并发送")
    print()
    print("🔄 持续对话模式:")
    print("   1. 选择'持续对话'标签")
    print("   2. 点击'开始持续对话'")
    print("   3. 正常说话，观察实时识别")
    print("   4. 系统自动发送识别完成的文本")
    print()
    print("🧪 测试页面功能:")
    print("   • 实时显示测试: 专门测试语音转文字显示")
    print("   • 语音模式测试: 综合功能测试")
    print("   • 浏览器兼容性检查")
    print()
    print("💡 使用技巧:")
    print("   • 说话清晰，语速适中")
    print("   • 在安静环境中使用")
    print("   • 观察颜色变化了解识别状态")
    print("   • 可在发送前编辑识别结果")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请解决上述问题后重试")
        return
    
    print("\n" + "=" * 70)
    print("🎉 实时语音转文字显示功能演示")
    print("=" * 70)
    print()
    print("本演示将展示 LiveTalking 的新功能：")
    print("• 实时语音转文字显示")
    print("• 临时/最终结果区分")
    print("• 语音识别状态指示")
    print("• 增强的用户体验")
    print()
    
    # 询问是否继续
    continue_demo = input("是否启动演示？(y/n): ").lower().strip()
    if continue_demo != 'y':
        print("演示已取消")
        return
    
    # 启动应用
    process = start_livetalking()
    if not process:
        print("❌ 无法启动应用")
        return
    
    try:
        # 等待服务启动
        if wait_for_service("http://127.0.0.1:8010/dashboard.html"):
            # 打开演示页面
            open_demo_pages()
            
            # 显示功能指南
            show_feature_guide()
            
            print("\n" + "=" * 70)
            print("🎉 实时语音转文字显示演示环境已准备就绪！")
            print("=" * 70)
            print()
            print("📱 请在浏览器中体验以下功能:")
            print("   1. 主界面 - 体验实时语音转文字显示")
            print("   2. 实时显示测试页面 - 专门测试新功能")
            print("   3. 语音模式测试页面 - 综合功能测试")
            print()
            print("🔍 观察要点:")
            print("   • 文本输入框中的实时覆盖显示")
            print("   • 临时结果（灰色斜体）和最终结果（蓝色加粗）")
            print("   • 右上角的语音指示器动画")
            print("   • 下方预览区域的状态信息")
            print()
            print("⌨️  按 Ctrl+C 停止演示")
            print("=" * 70)
            
            # 等待用户中断
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n\n🛑 用户中断演示")
        else:
            print("❌ 服务启动失败")
    
    finally:
        # 清理进程
        if process and process.poll() is None:
            print("🧹 清理进程...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            print("✅ 进程已清理")

if __name__ == "__main__":
    main()
