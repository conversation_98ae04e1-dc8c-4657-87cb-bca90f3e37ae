@echo off
chcp 65001 >nul
echo LiveTalking 启动脚本
echo ===================

echo 激活 conda 环境...
call conda activate nerfstream
if %errorlevel% neq 0 (
    echo ❌ 无法激活 nerfstream 环境，请检查 conda 环境
    pause
    exit /b 1
)

echo 显示原始代理设置...
echo   原始 HTTP_PROXY=%HTTP_PROXY%
echo   原始 HTTPS_PROXY=%HTTPS_PROXY%

echo 设置环境变量以绕过代理...
set NO_PROXY=127.0.0.1,localhost,*.local
set HTTP_PROXY=
set HTTPS_PROXY=
set http_proxy=
set https_proxy=
set PYTHONIOENCODING=utf-8
set REQUESTS_CA_BUNDLE=
set CURL_CA_BUNDLE=

echo 当前会话代理设置:
echo   NO_PROXY=%NO_PROXY%
echo   HTTP_PROXY=%HTTP_PROXY%
echo   HTTPS_PROXY=%HTTPS_PROXY%
echo   http_proxy=%http_proxy%
echo   https_proxy=%https_proxy%

echo 检查 GPT-SoVITS 服务...
curl -s --connect-timeout 5 http://127.0.0.1:9880/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ GPT-SoVITS 服务正在运行
) else (
    echo ❌ GPT-SoVITS 服务未运行，请先启动 GPT-SoVITS 服务
    echo 请确保 GPT-SoVITS 在 http://127.0.0.1:9880 运行
    pause
    exit /b 1
)

echo 检查参考音频文件...
if exist "D:\ai\Liv2\ref.wav" (
    echo ✅ 参考音频文件存在
) else (
    echo ❌ 参考音频文件不存在: D:\ai\Liv2\ref.wav
    echo 请确保参考音频文件路径正确
    pause
    exit /b 1
)

echo 启动 LiveTalking 应用...
echo 参数配置:
echo   - 传输方式: webrtc
echo   - 模型: wav2lip
echo   - 头像: wav2lip256_avatar1
echo   - TTS: gpt-sovits
echo   - TTS 服务器: http://127.0.0.1:9880
echo   - 参考音频: D:\ai\Liv2\ref.wav
echo   - 参考文本: 轻量化技术的连续覆盖网络，面向重庆复杂的道路交通
echo.
echo 正在启动应用，请稍候...

python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "D:\ai\Liv2\ref.wav" --REF_TEXT "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"

pause
