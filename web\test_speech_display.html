<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时语音转文字显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .speech-input-container {
            position: relative;
            margin: 20px 0;
        }
        .speech-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            padding: 10px 15px;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            border: 1px solid transparent;
            border-radius: 5px;
            background: transparent;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow: hidden;
            z-index: 1;
        }
        .speech-text-interim {
            color: #6c757d;
            background-color: rgba(108, 117, 125, 0.1);
            border-radius: 3px;
            padding: 1px 3px;
            font-style: italic;
        }
        .speech-text-final {
            color: #4361ee;
            background-color: rgba(67, 97, 238, 0.1);
            border-radius: 3px;
            padding: 1px 3px;
            font-weight: 500;
        }
        .speech-indicator {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: #dc3545;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            display: none;
            z-index: 2;
        }
        .speech-indicator.active {
            display: block;
            animation: pulse-indicator 1.5s infinite;
        }
        @keyframes pulse-indicator {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.05); }
            100% { opacity: 1; transform: scale(1); }
        }
        .test-textarea {
            width: 100%;
            height: 120px;
            padding: 10px 15px;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            font-size: 16px;
            resize: vertical;
            position: relative;
            z-index: 0;
        }
        .test-textarea:focus {
            z-index: 2;
            border-color: #4361ee;
            outline: none;
        }
        .speech-preview {
            margin-top: 10px;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 14px;
            display: none;
        }
        .speech-preview.active {
            display: block;
        }
        .speech-preview-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .speech-preview-text {
            color: #6c757d;
            font-style: italic;
        }
        .control-buttons {
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #4361ee;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #5a6268;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <a href="dashboard.html" class="back-link">← 返回主界面</a>
    
    <h1>实时语音转文字显示测试</h1>
    
    <div class="test-section">
        <h2>功能测试</h2>
        <div id="feature-status"></div>
        
        <div class="speech-input-container">
            <textarea class="test-textarea" id="test-textarea" placeholder="这里将显示实时语音识别结果..."></textarea>
            <div class="speech-overlay" id="speech-overlay"></div>
            <div class="speech-indicator" id="speech-indicator">🎤 识别中</div>
        </div>
        
        <div class="speech-preview" id="speech-preview">
            <div class="speech-preview-label">语音识别预览：</div>
            <div class="speech-preview-text" id="speech-preview-text">等待语音输入...</div>
        </div>
        
        <div class="control-buttons">
            <button class="btn btn-success" id="start-recognition">开始语音识别</button>
            <button class="btn btn-danger" id="stop-recognition" disabled>停止语音识别</button>
            <button class="btn btn-primary" id="clear-text">清除文本</button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>测试日志</h2>
        <div id="test-log" class="log"></div>
        <button class="btn btn-primary" onclick="clearLog()">清除日志</button>
    </div>

    <script>
        let recognition = null;
        let isRecognizing = false;
        let currentInterimText = '';
        let currentFinalText = '';
        
        function log(message) {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }
        
        function setStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function updateSpeechDisplay(interimText, finalText) {
            const textarea = document.getElementById('test-textarea');
            const speechOverlay = document.getElementById('speech-overlay');
            const speechIndicator = document.getElementById('speech-indicator');
            const speechPreview = document.getElementById('speech-preview');
            const speechPreviewText = document.getElementById('speech-preview-text');
            
            currentInterimText = interimText;
            currentFinalText = finalText;
            
            let displayText = '';
            let overlayHTML = '';
            
            if (finalText) {
                displayText += finalText;
                overlayHTML += `<span class="speech-text-final">${finalText}</span>`;
            }
            
            if (interimText) {
                if (finalText) {
                    displayText += ' ';
                    overlayHTML += ' ';
                }
                displayText += interimText;
                overlayHTML += `<span class="speech-text-interim">${interimText}</span>`;
            }
            
            textarea.value = displayText;
            speechOverlay.innerHTML = overlayHTML;
            
            if (interimText || finalText) {
                speechIndicator.classList.add('active');
                
                let previewText = '';
                if (finalText) {
                    previewText += `确认: "${finalText}"`;
                }
                if (interimText) {
                    if (finalText) previewText += ' | ';
                    previewText += `识别中: "${interimText}"`;
                }
                speechPreviewText.textContent = previewText;
                speechPreview.classList.add('active');
            } else {
                speechIndicator.classList.remove('active');
                speechPreviewText.textContent = '等待语音输入...';
                speechPreview.classList.remove('active');
            }
            
            if (displayText) {
                textarea.scrollTop = textarea.scrollHeight;
            }
        }
        
        function clearSpeechDisplay() {
            currentInterimText = '';
            currentFinalText = '';
            document.getElementById('test-textarea').value = '';
            document.getElementById('speech-overlay').innerHTML = '';
            document.getElementById('speech-indicator').classList.remove('active');
            document.getElementById('speech-preview').classList.remove('active');
            document.getElementById('speech-preview-text').textContent = '等待语音输入...';
        }
        
        function initSpeechRecognition() {
            if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
                setStatus('feature-status', '❌ 浏览器不支持语音识别功能', 'error');
                log('浏览器不支持语音识别');
                return false;
            }
            
            recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
            recognition.continuous = true;
            recognition.interimResults = true;
            recognition.lang = 'zh-CN';
            
            recognition.onstart = function() {
                log('语音识别已启动');
                setStatus('feature-status', '✅ 语音识别已启动，请说话...', 'info');
                document.getElementById('start-recognition').disabled = true;
                document.getElementById('stop-recognition').disabled = false;
                isRecognizing = true;
                
                document.getElementById('speech-preview').classList.add('active');
                document.getElementById('speech-preview-text').textContent = '正在启动语音识别...';
            };
            
            recognition.onresult = function(event) {
                let interimTranscript = '';
                let finalTranscript = '';
                
                for (let i = 0; i < event.results.length; ++i) {
                    if (event.results[i].isFinal) {
                        finalTranscript += event.results[i][0].transcript;
                    } else {
                        interimTranscript += event.results[i][0].transcript;
                    }
                }
                
                updateSpeechDisplay(interimTranscript, finalTranscript);
                
                log(`识别结果 - 临时: "${interimTranscript}", 最终: "${finalTranscript}"`);
            };
            
            recognition.onerror = function(event) {
                log(`语音识别错误: ${event.error}`);
                setStatus('feature-status', `❌ 语音识别错误: ${event.error}`, 'error');
                
                document.getElementById('speech-preview-text').textContent = `识别错误: ${event.error}`;
                document.getElementById('speech-preview').classList.add('active');
                
                setTimeout(() => {
                    document.getElementById('speech-preview').classList.remove('active');
                }, 3000);
            };
            
            recognition.onend = function() {
                log('语音识别已结束');
                setStatus('feature-status', '✅ 语音识别已结束', 'success');
                document.getElementById('start-recognition').disabled = false;
                document.getElementById('stop-recognition').disabled = true;
                isRecognizing = false;
                
                document.getElementById('speech-indicator').classList.remove('active');
                
                if (currentFinalText) {
                    setTimeout(() => {
                        document.getElementById('speech-preview').classList.remove('active');
                    }, 3000);
                } else {
                    document.getElementById('speech-preview').classList.remove('active');
                }
            };
            
            setStatus('feature-status', '✅ 语音识别功能已初始化', 'success');
            log('语音识别功能初始化成功');
            return true;
        }
        
        function startRecognition() {
            if (!recognition) {
                log('语音识别未初始化');
                return;
            }
            
            if (isRecognizing) {
                log('语音识别已在运行中');
                return;
            }
            
            clearSpeechDisplay();
            
            try {
                recognition.start();
                log('开始语音识别');
            } catch (e) {
                log(`启动语音识别失败: ${e.message}`);
                setStatus('feature-status', `❌ 启动失败: ${e.message}`, 'error');
            }
        }
        
        function stopRecognition() {
            if (!recognition || !isRecognizing) {
                log('没有正在运行的语音识别');
                return;
            }
            
            try {
                recognition.stop();
                log('停止语音识别');
            } catch (e) {
                log(`停止语音识别失败: ${e.message}`);
            }
        }
        
        function clearText() {
            clearSpeechDisplay();
            log('清除文本显示');
        }
        
        // 页面加载时初始化
        window.onload = function() {
            log('页面加载完成，初始化语音识别功能');
            
            if (initSpeechRecognition()) {
                // 绑定按钮事件
                document.getElementById('start-recognition').onclick = startRecognition;
                document.getElementById('stop-recognition').onclick = stopRecognition;
                document.getElementById('clear-text').onclick = clearText;
                
                log('所有功能初始化完成');
            } else {
                log('语音识别功能不可用');
            }
        };
        
        // 页面卸载时清理资源
        window.onbeforeunload = function() {
            if (recognition && isRecognizing) {
                try {
                    recognition.stop();
                } catch (e) {}
            }
        };
    </script>
</body>
</html>
