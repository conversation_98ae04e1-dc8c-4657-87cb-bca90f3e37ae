#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理绕过功能
"""

import os
import requests
import sys

def test_proxy_bypass():
    """测试代理绕过功能"""
    
    print("代理绕过测试")
    print("=" * 30)
    
    # 显示当前环境变量
    print("当前环境变量:")
    print(f"  HTTP_PROXY: {os.environ.get('HTTP_PROXY', '(未设置)')}")
    print(f"  HTTPS_PROXY: {os.environ.get('HTTPS_PROXY', '(未设置)')}")
    print(f"  NO_PROXY: {os.environ.get('NO_PROXY', '(未设置)')}")
    print()
    
    # 强制设置环境变量
    print("强制设置环境变量...")
    os.environ['HTTP_PROXY'] = ''
    os.environ['HTTPS_PROXY'] = ''
    os.environ['NO_PROXY'] = '127.0.0.1,localhost'
    
    print("设置后的环境变量:")
    print(f"  HTTP_PROXY: {os.environ.get('HTTP_PROXY', '(未设置)')}")
    print(f"  HTTPS_PROXY: {os.environ.get('HTTPS_PROXY', '(未设置)')}")
    print(f"  NO_PROXY: {os.environ.get('NO_PROXY', '(未设置)')}")
    print()
    
    # 测试连接
    server_url = "http://127.0.0.1:9880"
    
    print(f"测试连接到: {server_url}")
    
    try:
        # 方法1: 使用环境变量
        print("方法1: 使用环境变量...")
        response1 = requests.get(f"{server_url}/", timeout=5)
        print(f"✅ 方法1成功: {response1.status_code}")
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    try:
        # 方法2: 使用 Session 禁用代理
        print("方法2: 使用 Session 禁用代理...")
        session = requests.Session()
        session.proxies = {'http': None, 'https': None}
        response2 = session.get(f"{server_url}/", timeout=5)
        print(f"✅ 方法2成功: {response2.status_code}")
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
    
    try:
        # 方法3: 直接在请求中禁用代理
        print("方法3: 直接在请求中禁用代理...")
        response3 = requests.get(f"{server_url}/", proxies={'http': None, 'https': None}, timeout=5)
        print(f"✅ 方法3成功: {response3.status_code}")
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
    
    # 测试 TTS 端点
    print("\n测试 TTS 端点...")
    test_data = {
        'text': '测试',
        'text_lang': 'zh',
        'ref_audio_path': r'D:\ai\Liv2\ref.wav',
        'prompt_text': '测试',
        'prompt_lang': 'zh',
        'media_type': 'ogg',
        'streaming_mode': True
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'audio/ogg, audio/wav, audio/*, */*'
    }
    
    try:
        session = requests.Session()
        session.proxies = {'http': None, 'https': None}
        
        response = session.post(
            f"{server_url}/tts",
            json=test_data,
            headers=headers,
            stream=True,
            timeout=10
        )
        
        print(f"TTS 响应状态: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            if 'audio' in content_type:
                print("✅ TTS 端点工作正常!")
                return True
            else:
                print(f"❌ TTS 返回非音频内容: {content_type}")
                print(f"响应内容: {response.text[:200]}")
        else:
            print(f"❌ TTS 请求失败")
            print(f"响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ TTS 测试失败: {e}")
    
    return False

if __name__ == "__main__":
    success = test_proxy_bypass()
    if success:
        print("\n🎉 代理绕过测试成功!")
    else:
        print("\n💥 代理绕过测试失败!")
        print("\n建议:")
        print("1. 确保 GPT-SoVITS 服务正在运行")
        print("2. 检查防火墙设置")
        print("3. 尝试重启 GPT-SoVITS 服务")
