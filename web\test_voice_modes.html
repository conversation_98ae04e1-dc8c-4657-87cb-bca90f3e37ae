<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveTalking 语音模式测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button.primary {
            background-color: #007bff;
            color: white;
        }
        .test-button.success {
            background-color: #28a745;
            color: white;
        }
        .test-button.danger {
            background-color: #dc3545;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-link:hover {
            background-color: #5a6268;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <a href="dashboard.html" class="back-link">← 返回主界面</a>
    
    <h1>LiveTalking 语音模式测试</h1>
    
    <div class="test-section">
        <h2>功能检查</h2>
        <div id="feature-status"></div>
        <button class="test-button primary" onclick="checkFeatures()">检查浏览器支持</button>
    </div>
    
    <div class="test-section">
        <h2>语音识别测试</h2>
        <div id="recognition-status"></div>
        <button class="test-button success" onclick="testSpeechRecognition()">测试语音识别</button>
        <button class="test-button danger" onclick="stopSpeechRecognition()">停止识别</button>
        <div id="recognition-result" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>麦克风访问测试</h2>
        <div id="microphone-status"></div>
        <button class="test-button success" onclick="testMicrophone()">测试麦克风</button>
        <button class="test-button danger" onclick="stopMicrophone()">停止麦克风</button>
    </div>
    
    <div class="test-section">
        <h2>LiveTalking 连接测试</h2>
        <div id="connection-status"></div>
        <button class="test-button primary" onclick="testLiveTalkingConnection()">测试连接</button>
        <div id="connection-result" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>测试日志</h2>
        <div id="test-log" class="log"></div>
        <button class="test-button primary" onclick="clearLog()">清除日志</button>
    </div>

    <script>
        let recognition = null;
        let mediaStream = null;
        
        function log(message) {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function setStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }
        
        function checkFeatures() {
            log('开始检查浏览器功能支持...');
            
            const features = [];
            
            // 检查语音识别支持
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                features.push('✅ 语音识别 (Speech Recognition)');
                log('语音识别: 支持');
            } else {
                features.push('❌ 语音识别 (Speech Recognition)');
                log('语音识别: 不支持');
            }
            
            // 检查媒体设备支持
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                features.push('✅ 媒体设备访问 (getUserMedia)');
                log('媒体设备访问: 支持');
            } else {
                features.push('❌ 媒体设备访问 (getUserMedia)');
                log('媒体设备访问: 不支持');
            }
            
            // 检查 MediaRecorder 支持
            if (window.MediaRecorder) {
                features.push('✅ 媒体录制 (MediaRecorder)');
                log('媒体录制: 支持');
            } else {
                features.push('❌ 媒体录制 (MediaRecorder)');
                log('媒体录制: 不支持');
            }
            
            // 检查 WebRTC 支持
            if (window.RTCPeerConnection) {
                features.push('✅ WebRTC (RTCPeerConnection)');
                log('WebRTC: 支持');
            } else {
                features.push('❌ WebRTC (RTCPeerConnection)');
                log('WebRTC: 不支持');
            }
            
            const statusType = features.every(f => f.startsWith('✅')) ? 'success' : 'error';
            setStatus('feature-status', features.join('<br>'), statusType);
        }
        
        function testSpeechRecognition() {
            if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
                setStatus('recognition-status', '浏览器不支持语音识别', 'error');
                return;
            }
            
            log('开始测试语音识别...');
            
            recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
            recognition.continuous = true;
            recognition.interimResults = true;
            recognition.lang = 'zh-CN';
            
            recognition.onstart = function() {
                setStatus('recognition-status', '语音识别已启动，请说话...', 'info');
                log('语音识别已启动');
            };
            
            recognition.onresult = function(event) {
                let interimTranscript = '';
                let finalTranscript = '';
                
                for (let i = event.resultIndex; i < event.results.length; ++i) {
                    if (event.results[i].isFinal) {
                        finalTranscript += event.results[i][0].transcript;
                    } else {
                        interimTranscript += event.results[i][0].transcript;
                    }
                }
                
                const resultElement = document.getElementById('recognition-result');
                resultElement.innerHTML = `
                    <strong>实时识别:</strong> ${interimTranscript}<br>
                    <strong>最终结果:</strong> ${finalTranscript}
                `;
                
                if (finalTranscript) {
                    log(`语音识别结果: ${finalTranscript}`);
                }
            };
            
            recognition.onerror = function(event) {
                setStatus('recognition-status', `语音识别错误: ${event.error}`, 'error');
                log(`语音识别错误: ${event.error}`);
            };
            
            recognition.onend = function() {
                setStatus('recognition-status', '语音识别已停止', 'info');
                log('语音识别已停止');
            };
            
            try {
                recognition.start();
            } catch (e) {
                setStatus('recognition-status', `启动语音识别失败: ${e.message}`, 'error');
                log(`启动语音识别失败: ${e.message}`);
            }
        }
        
        function stopSpeechRecognition() {
            if (recognition) {
                recognition.stop();
                recognition = null;
                log('手动停止语音识别');
            }
        }
        
        function testMicrophone() {
            log('开始测试麦克风访问...');
            
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(function(stream) {
                    mediaStream = stream;
                    setStatus('microphone-status', '麦克风访问成功', 'success');
                    log('麦克风访问成功');
                    
                    // 显示音频流信息
                    const audioTracks = stream.getAudioTracks();
                    if (audioTracks.length > 0) {
                        const track = audioTracks[0];
                        log(`音频轨道: ${track.label}`);
                        log(`音频设置: ${JSON.stringify(track.getSettings())}`);
                    }
                })
                .catch(function(error) {
                    setStatus('microphone-status', `麦克风访问失败: ${error.message}`, 'error');
                    log(`麦克风访问失败: ${error.message}`);
                });
        }
        
        function stopMicrophone() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                mediaStream = null;
                setStatus('microphone-status', '麦克风已停止', 'info');
                log('麦克风已停止');
            }
        }
        
        function testLiveTalkingConnection() {
            log('开始测试 LiveTalking 连接...');
            
            // 测试主页面
            fetch('/dashboard.html')
                .then(response => {
                    if (response.ok) {
                        log('✅ dashboard.html 可访问');
                        return fetch('/human', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                text: '测试连接',
                                type: 'chat',
                                sessionid: 0
                            })
                        });
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(response => {
                    if (response.ok) {
                        log('✅ /human 端点可访问');
                        setStatus('connection-status', 'LiveTalking 服务正常', 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .catch(error => {
                    log(`❌ LiveTalking 连接失败: ${error.message}`);
                    setStatus('connection-status', `连接失败: ${error.message}`, 'error');
                });
        }
        
        // 页面加载时自动检查功能
        window.onload = function() {
            log('页面加载完成，开始自动检查...');
            checkFeatures();
        };
        
        // 页面卸载时清理资源
        window.onbeforeunload = function() {
            if (recognition) {
                recognition.stop();
            }
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
            }
        };
    </script>
</body>
</html>
