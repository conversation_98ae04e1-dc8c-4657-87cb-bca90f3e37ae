# LiveTalking 双语音交互模式使用指南

## 概述

LiveTalking 现在支持两种语音交互模式，并配备实时语音转文字显示功能，为用户提供更直观、灵活的对话体验：

1. **按住说话模式** (Push-to-Talk) - 原有功能增强
2. **持续对话模式** (Continuous Mode) - 新增功能
3. **实时语音转文字显示** - 全新功能

## 功能特性

### 按住说话模式 (Push-to-Talk)
- **操作方式**: 按住麦克风按钮说话，松开发送
- **适用场景**: 短句交流、精确控制发送时机
- **优势**:
  - 精确控制录音时间
  - 避免误触发
  - 适合嘈杂环境
- **实时显示**: 显示语音识别的临时和最终结果

### 持续对话模式 (Continuous Mode)
- **操作方式**: 点击开始后系统持续监听，自动识别并发送
- **适用场景**: 长时间对话、自然交流
- **优势**:
  - 解放双手，无需按住按钮
  - 自然对话体验
  - 适合连续交流
- **实时显示**: 持续显示语音识别进度和结果

### 实时语音转文字显示 (Real-time Speech Display)
- **功能**: 在文本输入框中实时显示语音识别结果
- **特性**:
  - 临时结果显示（灰色斜体，识别进行中）
  - 最终结果显示（蓝色加粗，识别完成）
  - 语音指示器（红色脉冲动画）
  - 预览区域（详细状态信息）
- **用户体验**:
  - 可视化语音转文字过程
  - 发送前预览和确认
  - 支持手动编辑识别结果

## 界面说明

### 模式切换
- 在对话模式标签页中，可以看到语音模式选择器
- 两个标签：**按住说话** 和 **持续对话**
- 点击标签即可切换模式

### 文本输入框增强
- **实时覆盖层**: 在文本输入框上方显示格式化的识别结果
- **语音指示器**: 右上角红色"🎤 识别中"标识
- **预览区域**: 输入框下方显示详细的识别状态
- **颜色编码**:
  - 灰色斜体：临时识别结果（识别进行中）
  - 蓝色加粗：最终识别结果（识别完成）

### 按住说话模式界面
- 圆形麦克风按钮
- 提示文字："按住说话，松开发送"
- 录音时按钮会变红并有脉冲动画
- 实时显示语音转文字结果

### 持续对话模式界面
- 大型圆形开始/停止按钮
- 状态指示器显示当前状态
- 开始按钮：绿色渐变
- 停止按钮：红色渐变，带脉冲动画
- 持续显示语音识别进度

## 使用步骤

### 首次使用准备
1. 确保浏览器支持语音识别功能
2. 允许浏览器访问麦克风权限
3. 确保网络连接正常

### 按住说话模式使用
1. 选择"按住说话"标签
2. 按住麦克风按钮
3. 开始说话
4. 松开按钮自动发送

### 持续对话模式使用
1. 选择"持续对话"标签
2. 点击绿色"开始"按钮
3. 系统开始监听，状态显示"正在监听..."
4. 正常说话，系统自动识别并发送
5. 点击红色"停止"按钮结束对话

## 状态指示

### 持续对话模式状态
- **点击开始持续对话**: 初始状态，等待用户启动
- **正在监听...**: 系统正在监听语音输入
- **识别错误，请重新开始**: 语音识别出现错误
- **麦克风访问失败**: 无法访问麦克风设备

### 视觉反馈
- 录音时有红色脉冲动画
- 按钮颜色变化指示状态
- 实时状态文字更新

## 技术要求

### 浏览器支持
- **推荐**: Chrome 25+, Edge 79+, Safari 14.1+
- **必需功能**:
  - Web Speech API (语音识别)
  - getUserMedia API (麦克风访问)
  - MediaRecorder API (音频录制)

### 系统要求
- 麦克风设备
- 稳定的网络连接
- 支持的操作系统：Windows 10+, macOS 10.15+, Linux

## 故障排除

### 常见问题

#### 1. 无法访问麦克风
**症状**: 提示"无法访问麦克风"
**解决方案**:
- 检查浏览器麦克风权限设置
- 确保麦克风设备正常工作
- 重新加载页面并允许麦克风访问

#### 2. 语音识别不工作
**症状**: 说话后没有识别结果
**解决方案**:
- 确认浏览器支持语音识别
- 检查网络连接
- 尝试更换浏览器

#### 3. 持续模式频繁停止
**症状**: 持续模式自动停止
**解决方案**:
- 检查麦克风权限
- 确保环境相对安静
- 重新启动持续模式

#### 4. 识别准确率低
**症状**: 语音识别结果不准确
**解决方案**:
- 说话清晰，语速适中
- 减少环境噪音
- 调整麦克风位置

### 测试工具

使用内置测试页面检查功能：

#### 1. 语音模式测试页面
```
http://127.0.0.1:8010/test_voice_modes.html
```
功能：
- 浏览器功能支持检查
- 语音识别测试
- 麦克风访问测试
- LiveTalking 连接测试

#### 2. 实时语音显示测试页面
```
http://127.0.0.1:8010/test_speech_display.html
```
功能：
- 实时语音转文字显示测试
- 临时/最终结果区分显示
- 语音指示器和预览功能测试
- 详细的识别过程日志

## 最佳实践

### 使用建议
1. **首次使用**: 先使用测试页面验证功能
2. **环境选择**: 在相对安静的环境中使用
3. **模式选择**: 
   - 短句交流 → 按住说话模式
   - 长时间对话 → 持续对话模式
4. **权限管理**: 及时允许浏览器权限请求

### 性能优化
1. 关闭不必要的浏览器标签页
2. 确保系统资源充足
3. 使用有线网络连接（如可能）

## 更新日志

### v1.2.0 (当前版本)
- ✅ 新增实时语音转文字显示功能
- ✅ 文本输入框实时覆盖层显示
- ✅ 临时/最终结果区分显示
- ✅ 语音识别状态指示器
- ✅ 语音识别预览区域
- ✅ 增强的错误处理和用户反馈
- ✅ 创建专门的实时显示测试页面

### v1.1.0 (双语音模式版本)
- ✅ 新增持续对话模式
- ✅ 改进语音识别准确性
- ✅ 添加模式切换功能
- ✅ 增强错误处理
- ✅ 添加状态指示器
- ✅ 创建测试工具页面

### v1.0.0 (基础版本)
- ✅ 按住说话模式
- ✅ 基础语音识别
- ✅ WebRTC 音视频传输

## 技术支持

如遇到问题，请：
1. 首先使用测试页面诊断问题
2. 检查浏览器控制台错误信息
3. 确认系统和浏览器版本
4. 查看本文档的故障排除部分

## 开发者信息

- **项目**: LiveTalking
- **功能**: 双语音交互模式
- **技术栈**: WebRTC, Web Speech API, JavaScript
- **兼容性**: 现代浏览器
