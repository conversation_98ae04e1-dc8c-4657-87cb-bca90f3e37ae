# LiveTalking + DeepSeek-R1:8b 启动脚本

Write-Host "LiveTalking + DeepSeek-R1:8b 启动脚本" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

# 清除代理设置
Write-Host "清除代理设置..." -ForegroundColor Yellow
$env:HTTP_PROXY = $null
$env:HTTPS_PROXY = $null
$env:http_proxy = $null
$env:https_proxy = $null
$env:NO_PROXY = "127.0.0.1,localhost,*.local"
$env:PYTHONIOENCODING = "utf-8"

# 验证代理设置
Write-Host "当前代理设置:" -ForegroundColor Green
Write-Host "  HTTP_PROXY: '$env:HTTP_PROXY'" -ForegroundColor Cyan
Write-Host "  HTTPS_PROXY: '$env:HTTPS_PROXY'" -ForegroundColor Cyan
Write-Host "  NO_PROXY: '$env:NO_PROXY'" -ForegroundColor Cyan

# 检查 Ollama 服务
Write-Host "检查 Ollama 服务..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:11434/api/tags" -TimeoutSec 5
    Write-Host "✅ Ollama 服务正在运行" -ForegroundColor Green
    
    $models = $response.models
    $deepseekFound = $false
    
    Write-Host "可用模型:" -ForegroundColor Cyan
    foreach ($model in $models) {
        Write-Host "  - $($model.name)" -ForegroundColor Cyan
        if ($model.name -like "*deepseek-r1*8b*") {
            $deepseekFound = $true
            Write-Host "    ✅ 找到 deepseek-r1:8b 模型" -ForegroundColor Green
        }
    }
    
    if (-not $deepseekFound) {
        Write-Host "❌ 未找到 deepseek-r1:8b 模型" -ForegroundColor Red
        Write-Host "请运行: ollama pull deepseek-r1:8b" -ForegroundColor Yellow
        
        $download = Read-Host "是否现在下载模型？(y/n)"
        if ($download -eq "y" -or $download -eq "Y") {
            Write-Host "正在下载 deepseek-r1:8b 模型..." -ForegroundColor Yellow
            ollama pull deepseek-r1:8b
        } else {
            Write-Host "请手动下载模型后再启动应用" -ForegroundColor Yellow
            exit 1
        }
    }
    
} catch {
    Write-Host "❌ Ollama 服务未运行" -ForegroundColor Red
    Write-Host "请先启动 Ollama 服务: ollama serve" -ForegroundColor Yellow
    
    $start = Read-Host "是否现在启动 Ollama 服务？(y/n)"
    if ($start -eq "y" -or $start -eq "Y") {
        Write-Host "正在启动 Ollama 服务..." -ForegroundColor Yellow
        Start-Process -FilePath "ollama" -ArgumentList "serve" -NoNewWindow
        Start-Sleep -Seconds 3
    } else {
        exit 1
    }
}

# 检查 GPT-SoVITS 服务
Write-Host "检查 GPT-SoVITS 服务..." -ForegroundColor Yellow
try {
    $webClient = New-Object System.Net.WebClient
    $webClient.Proxy = $null
    $response = $webClient.DownloadString("http://127.0.0.1:9880/")
    Write-Host "✅ GPT-SoVITS 服务正在运行" -ForegroundColor Green
} catch {
    Write-Host "❌ GPT-SoVITS 服务未运行" -ForegroundColor Red
    Write-Host "请确保 GPT-SoVITS 在 http://127.0.0.1:9880 运行" -ForegroundColor Yellow
    
    $continue = Read-Host "是否继续启动应用？(y/n)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# 检查参考音频文件
$refFile = "D:\ai\Liv2\ref.wav"
Write-Host "检查参考音频文件: $refFile" -ForegroundColor Yellow
if (Test-Path $refFile) {
    Write-Host "✅ 参考音频文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ 参考音频文件不存在: $refFile" -ForegroundColor Red
    Write-Host "请确保参考音频文件路径正确" -ForegroundColor Yellow
}

# 测试 DeepSeek 模型
Write-Host "测试 DeepSeek-R1:8b 模型..." -ForegroundColor Yellow
try {
    $testPayload = @{
        model = "deepseek-r1:8b"
        prompt = "你好"
        stream = $false
    } | ConvertTo-Json
    
    $testResponse = Invoke-RestMethod -Uri "http://localhost:11434/api/generate" -Method Post -Body $testPayload -ContentType "application/json" -TimeoutSec 30
    
    if ($testResponse.response) {
        Write-Host "✅ DeepSeek-R1:8b 模型测试成功" -ForegroundColor Green
    } else {
        Write-Host "❌ DeepSeek-R1:8b 模型测试失败" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ DeepSeek-R1:8b 模型测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 启动应用
Write-Host ""
Write-Host "启动 LiveTalking 应用..." -ForegroundColor Green
Write-Host "配置:" -ForegroundColor Cyan
Write-Host "  - LLM: DeepSeek-R1:8b (本地 Ollama)" -ForegroundColor Cyan
Write-Host "  - TTS: GPT-SoVITS" -ForegroundColor Cyan
Write-Host "  - 模型: wav2lip" -ForegroundColor Cyan
Write-Host "  - 传输: webrtc" -ForegroundColor Cyan

Write-Host ""
Write-Host "正在启动应用..." -ForegroundColor Green

python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "D:\ai\Liv2\ref.wav" --REF_TEXT "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"
