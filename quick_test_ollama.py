#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试 Ollama 连接
"""

import requests

def quick_test():
    print("快速测试 Ollama...")
    
    try:
        # 测试连接
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama 服务正在运行")
            
            models = response.json().get('models', [])
            print(f"可用模型: {len(models)} 个")
            
            for model in models:
                name = model.get('name', '')
                print(f"  - {name}")
                
            return True
        else:
            print(f"❌ Ollama 响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("请确保 Ollama 正在运行: ollama serve")
        return False

if __name__ == "__main__":
    quick_test()
