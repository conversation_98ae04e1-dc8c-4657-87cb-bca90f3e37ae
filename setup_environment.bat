@echo off
chcp 65001 >nul
echo LiveTalking 环境设置脚本
echo ========================

echo 激活 conda 环境...
call conda activate nerfstream
if %errorlevel% neq 0 (
    echo ❌ 无法激活 nerfstream 环境，请检查 conda 环境
    pause
    exit /b 1
)

echo ✅ 已激活 nerfstream 环境

echo 检查并安装必要的 Python 包...
echo 正在安装 flask...
pip install flask --quiet
if %errorlevel% neq 0 (
    echo ❌ flask 安装失败
    pause
    exit /b 1
)

echo 正在安装 flask_sockets...
pip install flask_sockets --quiet
if %errorlevel% neq 0 (
    echo ❌ flask_sockets 安装失败
    pause
    exit /b 1
)

echo 正在安装 aiohttp_cors...
pip install aiohttp_cors --quiet
if %errorlevel% neq 0 (
    echo ❌ aiohttp_cors 安装失败
    pause
    exit /b 1
)

echo 正在安装 aiortc...
pip install aiortc --quiet
if %errorlevel% neq 0 (
    echo ❌ aiortc 安装失败
    pause
    exit /b 1
)

echo ✅ 所有必要的包已安装完成

echo 设置环境变量以绕过代理...
set NO_PROXY=127.0.0.1,localhost
set HTTP_PROXY=
set HTTPS_PROXY=
set PYTHONIOENCODING=utf-8

echo 检查 GPT-SoVITS 服务...
curl -s --connect-timeout 5 http://127.0.0.1:9880/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ GPT-SoVITS 服务正在运行
) else (
    echo ❌ GPT-SoVITS 服务未运行，请先启动 GPT-SoVITS 服务
    echo 请确保 GPT-SoVITS 在 http://127.0.0.1:9880 运行
    pause
    exit /b 1
)

echo 检查参考音频文件...
if exist "D:\ai\Liv2\ref.wav" (
    echo ✅ 参考音频文件存在
) else (
    echo ❌ 参考音频文件不存在: D:\ai\Liv2\ref.wav
    echo 请确保参考音频文件路径正确
    pause
    exit /b 1
)

echo.
echo ✅ 环境设置完成！
echo 现在可以启动 LiveTalking 应用了
echo.
echo 启动命令:
echo python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "D:\ai\Liv2\ref.wav" --REF_TEXT "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"
echo.

set /p choice="是否现在启动应用？(y/n): "
if /i "%choice%"=="y" (
    echo 启动 LiveTalking 应用...
    python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "D:\ai\Liv2\ref.wav" --REF_TEXT "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"
) else (
    echo 环境已准备就绪，您可以手动运行上述命令启动应用
)

pause
