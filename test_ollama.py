#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Ollama 连接和 qwen:14b 模型
"""

import requests
import json
import time
import sys

def test_ollama_connection():
    """测试 Ollama 服务连接"""
    print("测试 Ollama 连接")
    print("=" * 30)
    
    # 测试 Ollama 服务是否运行
    try:
        print("🔄 检查 Ollama 服务...")
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama 服务正在运行")
            
            # 检查可用模型
            models = response.json().get('models', [])
            print(f"📋 可用模型数量: {len(models)}")
            
            deepseek_found = False
            for model in models:
                model_name = model.get('name', '')
                print(f"  - {model_name}")
                if 'deepseek-r1' in model_name.lower() and '8b' in model_name.lower():
                    deepseek_found = True
                    print(f"    ✅ 找到 deepseek-r1:8b 模型")

            if not deepseek_found:
                print("❌ 未找到 deepseek-r1:8b 模型")
                print("请运行: ollama pull deepseek-r1:8b")
                return False
                
        else:
            print(f"❌ Ollama 服务响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 Ollama 服务")
        print("请确保 Ollama 正在运行: ollama serve")
        return False
    except Exception as e:
        print(f"❌ 检查 Ollama 服务时出错: {e}")
        return False
    
    return True

def test_deepseek_model():
    """测试 deepseek-r1:8b 模型"""
    print("\n测试 deepseek-r1:8b 模型")
    print("=" * 30)

    url = "http://localhost:11434/api/generate"
    test_message = "你好，请简单介绍一下你自己。"

    payload = {
        "model": "deepseek-r1:8b",
        "prompt": test_message,
        "stream": False
    }
    
    try:
        print(f"🔄 发送测试消息: {test_message}")
        start_time = time.time()
        
        response = requests.post(url, json=payload, timeout=60)
        response.raise_for_status()
        
        end_time = time.time()
        response_time = end_time - start_time
        
        data = response.json()
        text = data.get("response", "")
        
        print(f"✅ 模型响应成功")
        print(f"⏱️  响应时间: {response_time:.2f} 秒")
        print(f"📝 响应内容: {text[:200]}{'...' if len(text) > 200 else ''}")
        
        # 检查响应质量
        if len(text.strip()) > 0:
            print("✅ 响应内容正常")
            return True
        else:
            print("❌ 响应内容为空")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 模型响应超时（60秒）")
        print("提示: deepseek-r1:8b 模型首次加载可能需要一些时间")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试模型时出错: {e}")
        return False

def test_llm_integration():
    """测试与 LiveTalking 的集成"""
    print("\n测试 LiveTalking 集成")
    print("=" * 30)
    
    try:
        # 导入 llm 模块
        import sys
        import os
        sys.path.append('.')
        
        from llm import llm_response
        
        # 创建模拟的 nerfreal 对象
        class MockNerfReal:
            def __init__(self):
                self.messages = []
            
            def put_msg_txt(self, text):
                print(f"📤 TTS 输出: {text}")
                self.messages.append(text)
        
        mock_nerfreal = MockNerfReal()
        
        print("🔄 测试 LLM 集成...")
        test_message = "请用一句话介绍人工智能。"
        print(f"💬 输入: {test_message}")
        
        llm_response(test_message, mock_nerfreal)
        
        if mock_nerfreal.messages:
            print("✅ LLM 集成测试成功")
            print(f"📋 生成的消息数量: {len(mock_nerfreal.messages)}")
            return True
        else:
            print("❌ LLM 集成测试失败：没有生成消息")
            return False
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Ollama + deepseek-r1:8b 测试工具")
    print("=" * 40)
    
    # 测试 1: Ollama 连接
    if not test_ollama_connection():
        print("\n💥 Ollama 连接测试失败")
        print("\n🔧 解决建议:")
        print("1. 确保 Ollama 已安装: https://ollama.ai/")
        print("2. 启动 Ollama 服务: ollama serve")
        print("3. 下载 deepseek-r1:8b 模型: ollama pull deepseek-r1:8b")
        sys.exit(1)
    
    # 测试 2: deepseek-r1:8b 模型
    if not test_deepseek_model():
        print("\n💥 deepseek-r1:8b 模型测试失败")
        print("\n🔧 解决建议:")
        print("1. 确保模型已下载: ollama pull deepseek-r1:8b")
        print("2. 检查系统资源（内存、GPU）")
        print("3. 等待模型完全加载")
        sys.exit(1)
    
    # 测试 3: LiveTalking 集成
    if not test_llm_integration():
        print("\n💥 LiveTalking 集成测试失败")
        print("\n🔧 解决建议:")
        print("1. 检查 llm.py 文件是否存在")
        print("2. 确保所有依赖已安装")
        sys.exit(1)
    
    print("\n🎉 所有测试通过！")
    print("✅ Ollama 服务正常")
    print("✅ deepseek-r1:8b 模型可用")
    print("✅ LiveTalking 集成正常")
    print("\n现在可以启动 LiveTalking 应用了！")

if __name__ == "__main__":
    main()
