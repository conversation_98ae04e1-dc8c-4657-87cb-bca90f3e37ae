# LiveTalking 启动脚本
# 解决代理问题并启动应用

Write-Host "LiveTalking 启动脚本" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green

# 显示原始代理设置
Write-Host "原始代理设置:" -ForegroundColor Yellow
Write-Host "  HTTP_PROXY: $env:HTTP_PROXY" -ForegroundColor Cyan
Write-Host "  HTTPS_PROXY: $env:HTTPS_PROXY" -ForegroundColor Cyan

# 设置环境变量以绕过代理
Write-Host "设置环境变量以绕过代理..." -ForegroundColor Yellow
$env:NO_PROXY = "127.0.0.1,localhost,*.local"
$env:HTTP_PROXY = ""
$env:HTTPS_PROXY = ""
$env:http_proxy = ""
$env:https_proxy = ""
$env:REQUESTS_CA_BUNDLE = ""
$env:CURL_CA_BUNDLE = ""

# 验证设置
Write-Host "当前会话代理设置:" -ForegroundColor Green
Write-Host "  HTTP_PROXY: '$env:HTTP_PROXY'" -ForegroundColor Cyan
Write-Host "  HTTPS_PROXY: '$env:HTTPS_PROXY'" -ForegroundColor Cyan
Write-Host "  NO_PROXY: '$env:NO_PROXY'" -ForegroundColor Cyan

# 设置控制台编码为 UTF-8
Write-Host "设置控制台编码为 UTF-8..." -ForegroundColor Yellow
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$env:PYTHONIOENCODING = "utf-8"

# 检查 GPT-SoVITS 服务是否运行
Write-Host "检查 GPT-SoVITS 服务..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:9880/" -TimeoutSec 5 -UseBasicParsing
    Write-Host "✅ GPT-SoVITS 服务正在运行" -ForegroundColor Green
} catch {
    Write-Host "❌ GPT-SoVITS 服务未运行，请先启动 GPT-SoVITS 服务" -ForegroundColor Red
    Write-Host "请确保 GPT-SoVITS 在 http://127.0.0.1:9880 运行" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查参考音频文件
$refFile = "D:\ai\Liv2\ref.wav"
Write-Host "检查参考音频文件: $refFile" -ForegroundColor Yellow
if (Test-Path $refFile) {
    Write-Host "✅ 参考音频文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ 参考音频文件不存在: $refFile" -ForegroundColor Red
    Write-Host "请确保参考音频文件路径正确" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 启动应用
Write-Host "启动 LiveTalking 应用..." -ForegroundColor Yellow
Write-Host "参数配置:" -ForegroundColor Cyan
Write-Host "  - 传输方式: webrtc" -ForegroundColor Cyan
Write-Host "  - 模型: wav2lip" -ForegroundColor Cyan
Write-Host "  - 头像: wav2lip256_avatar1" -ForegroundColor Cyan
Write-Host "  - TTS: gpt-sovits" -ForegroundColor Cyan
Write-Host "  - TTS 服务器: http://127.0.0.1:9880" -ForegroundColor Cyan
Write-Host "  - 参考音频: $refFile" -ForegroundColor Cyan
Write-Host "  - 参考文本: 轻量化技术的连续覆盖网络，面向重庆复杂的道路交通" -ForegroundColor Cyan

Write-Host ""
Write-Host "正在启动应用，请稍候..." -ForegroundColor Green

python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "D:\ai\Liv2\ref.wav" --REF_TEXT "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"
