import time
import requests
import re
from basereal import BaseReal
from logger import logger

def llm_response(message, nerfreal: BaseReal):
    start = time.perf_counter()
    # Ollama 本地API调用
    url = "http://localhost:11434/api/generate"
    payload = {
        "model": "qwen:14b",
        "prompt": message,
        "stream": False,
        "options": {
            "temperature": 0.7,
            "top_p": 0.9,
            "max_tokens": 512
        }
    }
    try:
        logger.info(f"发送消息到 Ollama: {message[:50]}...")
        response = requests.post(url, json=payload, timeout=60)
        response.raise_for_status()
        data = response.json()
        text = data.get("response", "")

        if not text.strip():
            logger.warning("Ollama 返回空响应")
            nerfreal.put_msg_txt("[LLM响应为空]")
            return

    except requests.exceptions.ConnectionError:
        logger.error("无法连接到 Ollama 服务，请确保 Ollama 正在运行")
        nerfreal.put_msg_txt("[无法连接到本地LLM服务，请检查Ollama是否运行]")
        return
    except requests.exceptions.Timeout:
        logger.error("Ollama 响应超时")
        nerfreal.put_msg_txt("[LLM响应超时]")
        return
    except Exception as e:
        logger.error(f"Ollama LLM 调用失败: {e}")
        nerfreal.put_msg_txt("[本地LLM服务异常]")
        return
    end = time.perf_counter()
    logger.info(f"llm Time to get response: {end-start}s, response length: {len(text)}")
    # 过滤掉非文字字符（如*等），只保留中英文、数字和常用标点
    filtered_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9，。！？：；,.!?:;\s]', '', text)
    result = ""
    lastpos = 0
    for i, char in enumerate(filtered_text):
        if char in ",.!;:，。！？：；":
            result = result + filtered_text[lastpos:i+1]
            lastpos = i+1
            if len(result) > 10:
                logger.info(result)
                nerfreal.put_msg_txt(result)
                result = ""
    result = result + filtered_text[lastpos:]
    nerfreal.put_msg_txt(result)    