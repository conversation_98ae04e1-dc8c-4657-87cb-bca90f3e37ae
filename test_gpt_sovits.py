#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 GPT-SoVITS TTS 服务的脚本
"""

import requests
import json
import sys
import os
from pathlib import Path

# 禁用代理，直接连接本地服务
os.environ['NO_PROXY'] = '127.0.0.1,localhost'
os.environ['HTTP_PROXY'] = ''
os.environ['HTTPS_PROXY'] = ''

def test_gpt_sovits_service(server_url, ref_file, ref_text, test_text):
    """测试 GPT-SoVITS TTS 服务"""
    
    print(f"测试 GPT-SoVITS 服务: {server_url}")
    print(f"参考音频文件: {ref_file}")
    print(f"参考文本: {ref_text}")
    print(f"测试文本: {test_text}")
    print("-" * 50)
    
    # 检查参考音频文件是否存在
    if not os.path.exists(ref_file):
        print(f"❌ 错误: 参考音频文件不存在: {ref_file}")
        return False
    
    # 准备请求数据
    req_data = {
        'text': test_text,
        'text_lang': 'zh',
        'ref_audio_path': ref_file,
        'prompt_text': ref_text,
        'prompt_lang': 'zh',
        'media_type': 'ogg',
        'streaming_mode': True
    }
    
    try:
        print("🔄 正在测试服务连接...")
        
        # 首先测试服务是否可达
        try:
            # 禁用代理的会话
            session = requests.Session()
            session.proxies = {'http': None, 'https': None}

            health_response = session.get(f"{server_url}/", timeout=5)
            print(f"✅ 服务可达，状态码: {health_response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ 服务不可达: {e}")
            return False
        
        print("🔄 正在发送 TTS 请求...")
        
        # 发送 TTS 请求
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'audio/ogg, audio/wav, audio/*, */*'
        }
        
        response = session.post(
            f"{server_url}/tts",
            json=req_data,
            headers=headers,
            stream=True,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"❌ 错误内容: {response.text[:500]}")
            return False
        
        # 检查响应内容类型
        content_type = response.headers.get('content-type', '').lower()
        print(f"📊 内容类型: {content_type}")
        
        if 'text/html' in content_type or 'text/plain' in content_type:
            print(f"❌ 服务返回了文本而不是音频数据")
            print(f"❌ 响应内容: {response.text[:500]}")
            return False
        
        # 读取响应数据
        total_size = 0
        chunk_count = 0
        first_chunk_preview = None
        
        print("🔄 正在接收音频数据...")
        
        for chunk in response.iter_content(chunk_size=1024):
            if chunk:
                chunk_count += 1
                total_size += len(chunk)
                
                if chunk_count == 1:
                    first_chunk_preview = chunk[:50]  # 保存前50字节用于检查
                    
                    # 检查第一个chunk是否看起来像文本
                    try:
                        text_content = chunk.decode('utf-8', errors='ignore')
                        if any(keyword in text_content.lower() for keyword in ['error', 'html', '<html>', '<!doctype']):
                            print(f"❌ 第一个数据块看起来像错误文本: {text_content[:200]}")
                            return False
                    except:
                        pass  # 解码失败说明可能是二进制数据，这是好的
                
                if chunk_count <= 5:  # 只显示前5个chunk的信息
                    print(f"📦 接收到第 {chunk_count} 个数据块，大小: {len(chunk)} 字节")
        
        print(f"✅ 成功接收音频数据!")
        print(f"📊 总数据块数: {chunk_count}")
        print(f"📊 总数据大小: {total_size} 字节")
        
        if first_chunk_preview:
            print(f"📊 第一个数据块预览 (前50字节): {first_chunk_preview}")
        
        if total_size > 0:
            print("✅ GPT-SoVITS 服务工作正常!")
            return True
        else:
            print("❌ 没有接收到音频数据")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接错误 - 请检查 TTS 服务是否在 {server_url} 运行")
        return False
    except Exception as e:
        print(f"❌ 意外错误: {e}")
        return False

def main():
    """主函数"""
    
    # 默认参数
    server_url = "http://127.0.0.1:9880"
    ref_file = r"D:\ai\Liv2\ref.wav"
    ref_text = "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"
    test_text = "你好，这是一个测试。"
    
    # 从命令行参数获取配置
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    if len(sys.argv) > 2:
        ref_file = sys.argv[2]
    if len(sys.argv) > 3:
        ref_text = sys.argv[3]
    if len(sys.argv) > 4:
        test_text = sys.argv[4]
    
    print("GPT-SoVITS TTS 服务测试工具")
    print("=" * 50)
    
    success = test_gpt_sovits_service(server_url, ref_file, ref_text, test_text)
    
    if success:
        print("\n🎉 测试成功! GPT-SoVITS 服务工作正常。")
        sys.exit(0)
    else:
        print("\n💥 测试失败! 请检查 GPT-SoVITS 服务配置。")
        print("\n🔧 故障排除建议:")
        print("1. 确保 GPT-SoVITS 服务正在运行")
        print("2. 检查服务地址和端口是否正确")
        print("3. 确保参考音频文件存在且可访问")
        print("4. 检查服务日志是否有错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
