#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的 GPT-SoVITS TTS 测试脚本
"""

import sys
import os
import time

# 禁用代理
os.environ['NO_PROXY'] = '127.0.0.1,localhost'
os.environ['HTTP_PROXY'] = ''
os.environ['HTTPS_PROXY'] = ''

try:
    import requests
    print("✅ requests 模块可用")
except ImportError:
    print("❌ requests 模块不可用")
    sys.exit(1)

def test_simple_connection():
    """简单的连接测试"""
    server_url = "http://127.0.0.1:9880"
    
    print(f"测试连接到: {server_url}")
    
    try:
        # 创建禁用代理的会话
        session = requests.Session()
        session.proxies = {'http': None, 'https': None}
        
        # 测试根路径
        print("🔄 测试根路径...")
        response = session.get(f"{server_url}/", timeout=5)
        print(f"✅ 根路径响应: {response.status_code}")
        
        # 测试 TTS 端点
        print("🔄 测试 TTS 端点...")
        test_data = {
            'text': '你好',
            'text_lang': 'zh',
            'ref_audio_path': r'D:\ai\Liv2\ref.wav',
            'prompt_text': '测试',
            'prompt_lang': 'zh',
            'media_type': 'ogg',
            'streaming_mode': True
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'audio/ogg, audio/wav, audio/*, */*'
        }
        
        response = session.post(
            f"{server_url}/tts",
            json=test_data,
            headers=headers,
            stream=True,
            timeout=10
        )
        
        print(f"📊 TTS 响应状态: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '').lower()
            if 'audio' in content_type:
                print("✅ 成功获取音频响应!")
                
                # 读取一些数据
                chunk_count = 0
                total_size = 0
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        chunk_count += 1
                        total_size += len(chunk)
                        if chunk_count >= 5:  # 只读取前5个chunk
                            break
                
                print(f"📊 读取了 {chunk_count} 个数据块，总大小: {total_size} 字节")
                return True
            else:
                print(f"❌ 响应不是音频格式: {content_type}")
                print(f"❌ 响应内容: {response.text[:200]}")
        else:
            print(f"❌ TTS 请求失败: {response.text[:200]}")
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
    except requests.exceptions.Timeout as e:
        print(f"❌ 超时错误: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    
    return False

def main():
    print("简化的 GPT-SoVITS TTS 测试")
    print("=" * 40)
    
    success = test_simple_connection()
    
    if success:
        print("\n🎉 测试成功!")
        
        # 现在测试我们修改后的 ttsreal.py
        print("\n🔄 测试修改后的 TTS 类...")
        try:
            # 添加当前目录到路径
            sys.path.insert(0, '.')
            
            from ttsreal import SovitsTTS, State
            
            # 创建一个模拟的 opt 对象
            class MockOpt:
                def __init__(self):
                    self.fps = 50
                    self.REF_FILE = r'D:\ai\Liv2\ref.wav'
                    self.REF_TEXT = '轻量化技术的连续覆盖网络，面向重庆复杂的道路交通'
                    self.TTS_SERVER = 'http://127.0.0.1:9880'
            
            # 创建一个模拟的 parent 对象
            class MockParent:
                def put_audio_frame(self, frame, eventpoint=None):
                    print(f"📦 接收到音频帧: {len(frame)} 样本, 事件: {eventpoint}")
            
            opt = MockOpt()
            parent = MockParent()
            
            tts = SovitsTTS(opt, parent)
            
            print("✅ SovitsTTS 类创建成功")
            
            # 测试文本转音频
            test_text = "系统: 欢迎使用livetalking，请点击开始连接按钮开始对话"
            print(f"🔄 测试文本转音频: {test_text}")
            
            tts.put_msg_txt(test_text)
            
            # 等待一段时间让处理完成
            time.sleep(2)
            
            print("✅ TTS 测试完成")
            
        except Exception as e:
            print(f"❌ TTS 类测试失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("\n💥 连接测试失败!")

if __name__ == "__main__":
    main()
