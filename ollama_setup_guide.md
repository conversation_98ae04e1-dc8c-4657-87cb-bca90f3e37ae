# Ollama + qwen:14b 配置指南

## 1. 安装 Ollama

### Windows 安装
1. 访问 [Ollama 官网](https://ollama.ai/)
2. 下载 Windows 版本安装包
3. 运行安装程序

### 验证安装
```powershell
ollama --version
```

## 2. 下载 qwen:14b 模型

```powershell
# 下载 qwen:14b 模型（约 8GB）
ollama pull qwen:14b
```

**注意**: qwen:14b 模型较大，下载可能需要一些时间，请确保网络连接稳定。

## 3. 启动 Ollama 服务

```powershell
# 启动 Ollama 服务
ollama serve
```

服务将在 `http://localhost:11434` 运行。

## 4. 验证配置

运行测试脚本：
```powershell
python test_ollama.py
```

## 5. 系统要求

### 最低要求
- **内存**: 16GB RAM（推荐 32GB）
- **存储**: 至少 10GB 可用空间
- **CPU**: 现代多核处理器

### 推荐配置
- **GPU**: NVIDIA GPU（支持 CUDA）
- **内存**: 32GB+ RAM
- **存储**: SSD 存储

## 6. 常见问题

### Q: 模型加载很慢怎么办？
A: qwen:14b 是大模型，首次加载需要时间。确保有足够的内存和存储空间。

### Q: 出现内存不足错误？
A: 
- 关闭其他占用内存的应用
- 考虑使用更小的模型，如 `qwen:7b`
- 增加系统内存

### Q: 无法连接到 Ollama 服务？
A: 
- 确保 `ollama serve` 正在运行
- 检查端口 11434 是否被占用
- 检查防火墙设置

### Q: 想使用其他模型？
A: 修改 `llm.py` 中的模型名称：
```python
payload = {
    "model": "qwen:7b",  # 或其他模型
    "prompt": message,
    "stream": False
}
```

## 7. 可用的 Qwen 模型

- `qwen:0.5b` - 最小模型，速度快
- `qwen:1.8b` - 小模型，平衡性能
- `qwen:4b` - 中等模型
- `qwen:7b` - 较大模型，质量好
- `qwen:14b` - 大模型，质量最佳
- `qwen:32b` - 超大模型（需要大量内存）

## 8. 性能优化

### 使用 GPU 加速
如果有 NVIDIA GPU：
```powershell
# 安装 CUDA 版本的 Ollama
# Ollama 会自动检测并使用 GPU
```

### 调整模型参数
在 `llm.py` 中可以调整：
- `temperature`: 控制创造性（0.1-1.0）
- `top_p`: 控制多样性（0.1-1.0）
- `max_tokens`: 最大输出长度

## 9. 启动 LiveTalking

配置完成后，使用以下命令启动：
```powershell
cd D:\ai\LiveTalking
conda activate nerfstream
$env:HTTP_PROXY = $null
$env:HTTPS_PROXY = $null
$env:NO_PROXY = "127.0.0.1,localhost,*.local"
python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "D:\ai\Liv2\ref.wav" --REF_TEXT "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"
```

## 10. 故障排除

如果遇到问题，请按顺序检查：
1. Ollama 服务是否运行：`ollama list`
2. 模型是否下载：`ollama list`
3. 网络连接：`curl http://localhost:11434/api/tags`
4. 运行测试：`python test_ollama.py`
