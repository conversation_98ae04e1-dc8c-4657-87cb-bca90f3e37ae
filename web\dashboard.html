<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>livetalking数字人交互平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --background-color: #f8f9fa;
            --card-bg: #ffffff;
            --text-color: #212529;
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            min-height: 100vh;
            padding-top: 20px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            border: none;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            padding: 15px 20px;
            border-bottom: none;
        }

        .video-container {
            position: relative;
            width: 100%;
            background-color: #000;
            border-radius: var(--border-radius);
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        video {
            max-width: 100%;
            max-height: 100%;
            display: block;
            border-radius: var(--border-radius);
        }

        .controls-container {
            padding: 20px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .form-control {
            border-radius: var(--border-radius);
            padding: 10px 15px;
            border: 1px solid #ced4da;
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }

        .status-connected {
            background-color: #28a745;
        }

        .status-disconnected {
            background-color: #dc3545;
        }

        .status-connecting {
            background-color: #ffc107;
        }

        .asr-container {
            height: 300px;
            overflow-y: auto;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            border: 1px solid #ced4da;
        }

        .asr-text {
            margin-bottom: 10px;
            padding: 10px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .user-message {
            background-color: #e3f2fd;
            border-left: 4px solid var(--primary-color);
        }

        .system-message {
            background-color: #f1f8e9;
            border-left: 4px solid #8bc34a;
        }

        .recording-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: rgba(220, 53, 69, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            display: none;
        }

        .recording-indicator.active {
            display: flex;
            align-items: center;
        }

        .recording-indicator .blink {
            width: 10px;
            height: 10px;
            background-color: #fff;
            border-radius: 50%;
            margin-right: 5px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0.3; }
            100% { opacity: 1; }
        }

        .mode-switch {
            margin-bottom: 20px;
        }

        .nav-tabs .nav-link {
            color: var(--text-color);
            border: none;
            padding: 10px 20px;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            background-color: var(--card-bg);
            border-bottom: 3px solid var(--primary-color);
            font-weight: 600;
        }

        .tab-content {
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .settings-panel {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            margin-top: 15px;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px 0;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .voice-record-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            margin: 0 auto;
        }
        
        .voice-record-btn:hover {
            background-color: var(--secondary-color);
            transform: scale(1.05);
        }
        
        .voice-record-btn:active {
            background-color: #dc3545;
            transform: scale(0.95);
        }
        
        .voice-record-btn i {
            font-size: 24px;
        }
        
        .voice-record-label {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
            color: #6c757d;
        }

        .voice-mode-switch {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            border: 1px solid #dee2e6;
        }

        .voice-mode-tabs {
            display: flex;
            background-color: #e9ecef;
            border-radius: var(--border-radius);
            padding: 4px;
            margin-bottom: 15px;
        }

        .voice-mode-tab {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            background: none;
            border: none;
            border-radius: calc(var(--border-radius) - 4px);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: #6c757d;
        }

        .voice-mode-tab.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .continuous-controls {
            display: none;
            text-align: center;
        }

        .continuous-controls.active {
            display: block;
        }

        .continuous-btn {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            margin: 0 auto 15px;
            font-size: 28px;
        }

        .continuous-btn.start {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .continuous-btn.start:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: scale(1.05);
        }

        .continuous-btn.stop {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            animation: pulse-red 2s infinite;
        }

        .continuous-btn.stop:hover {
            background: linear-gradient(135deg, #c82333, #dc2626);
            transform: scale(1.05);
        }

        @keyframes pulse-red {
            0% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 20px rgba(220, 53, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
            }
        }

        .voice-status {
            text-align: center;
            font-size: 14px;
            color: #6c757d;
            margin-top: 10px;
        }

        .voice-status.recording {
            color: #dc3545;
            font-weight: 600;
        }

        .voice-status.idle {
            color: #28a745;
        }

        .push-to-talk-controls {
            text-align: center;
        }

        .push-to-talk-controls.active {
            display: block;
        }

        /* 语音识别预览样式 */

        .speech-preview {
            margin-top: 5px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: var(--border-radius);
            font-size: 14px;
            display: none;
        }

        .speech-preview.active {
            display: block;
        }

        .speech-preview-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .speech-preview-text {
            color: #6c757d;
            font-style: italic;
        }
        
        .video-size-control {
            margin-top: 15px;
        }
        
        .recording-pulse {
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(220, 53, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">livetalking数字人交互平台</h1>
            </div>
        </div>

        <div class="row">
            <!-- 视频区域 -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <span class="status-indicator status-disconnected" id="connection-status"></span>
                            <span id="status-text">未连接</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="video-container">
                            <video id="video" autoplay playsinline></video>
                            <div class="recording-indicator" id="recording-indicator">
                                <div class="blink"></div>
                                <span>录制中</span>
                            </div>
                        </div>
                        
                        <div class="controls-container">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-primary w-100" id="start">
                                        <i class="bi bi-play-fill"></i> 开始连接
                                    </button>
                                    <button class="btn btn-danger w-100" id="stop" style="display: none;">
                                        <i class="bi bi-stop-fill"></i> 停止连接
                                    </button>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex">
                                        <button class="btn btn-outline-primary flex-grow-1 me-2" id="btn_start_record">
                                            <i class="bi bi-record-fill"></i> 开始录制
                                        </button>
                                        <button class="btn btn-outline-danger flex-grow-1" id="btn_stop_record" disabled>
                                            <i class="bi bi-stop-fill"></i> 停止录制
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="video-size-control">
                                        <label for="video-size-slider" class="form-label">视频大小调节: <span id="video-size-value">100%</span></label>
                                        <input type="range" class="form-range" id="video-size-slider" min="50" max="150" value="100">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="settings-panel mt-3">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="use-stun">
                                            <label class="form-check-label" for="use-stun">使用STUN服务器</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧交互 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="interaction-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="chat-tab" data-bs-toggle="tab" data-bs-target="#chat" type="button" role="tab" aria-controls="chat" aria-selected="true">对话模式</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="tts-tab" data-bs-toggle="tab" data-bs-target="#tts" type="button" role="tab" aria-controls="tts" aria-selected="false">朗读模式</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="interaction-tabs-content">
                            <!-- 对话模式 -->
                            <div class="tab-pane fade show active" id="chat" role="tabpanel" aria-labelledby="chat-tab">
                                <div class="asr-container mb-3" id="chat-messages">
                                    <div class="asr-text system-message">
                                        系统: 欢迎使用livetalking，请点击"开始连接"按钮开始对话。
                                    </div>
                                </div>

                                <form id="chat-form">
                                    <div class="input-group mb-3">
                                        <textarea class="form-control" id="chat-message" rows="3" placeholder="输入您想对数字人说的话..."></textarea>
                                        <button class="btn btn-primary" type="submit">
                                            <i class="bi bi-send"></i> 发送
                                        </button>
                                    </div>
                                    <!-- 语音识别预览 -->
                                    <div class="speech-preview" id="speech-preview">
                                        <div class="speech-preview-label">语音识别预览：</div>
                                        <div class="speech-preview-text" id="speech-preview-text">等待语音输入...</div>
                                    </div>
                                </form>

                                <!-- 语音交互模式选择 -->
                                <div class="voice-mode-switch">
                                    <div class="voice-mode-tabs">
                                        <button class="voice-mode-tab active" data-mode="push-to-talk">
                                            <i class="bi bi-hand-index"></i> 按住说话
                                        </button>
                                        <button class="voice-mode-tab" data-mode="continuous">
                                            <i class="bi bi-mic"></i> 持续对话
                                        </button>
                                    </div>

                                    <!-- 按住说话模式 -->
                                    <div class="push-to-talk-controls active" id="push-to-talk-controls">
                                        <div class="voice-record-btn" id="voice-record-btn">
                                            <i class="bi bi-mic-fill"></i>
                                        </div>
                                        <div class="voice-record-label">按住说话，松开发送</div>
                                    </div>

                                    <!-- 持续对话模式 -->
                                    <div class="continuous-controls" id="continuous-controls">
                                        <button class="continuous-btn start" id="continuous-start-btn">
                                            <i class="bi bi-mic-fill"></i>
                                        </button>
                                        <button class="continuous-btn stop" id="continuous-stop-btn" style="display: none;">
                                            <i class="bi bi-stop-fill"></i>
                                        </button>
                                        <div class="voice-status idle" id="voice-status">点击开始持续对话</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 朗读模式 -->
                            <div class="tab-pane fade" id="tts" role="tabpanel" aria-labelledby="tts-tab">
                                <form id="echo-form">
                                    <div class="mb-3">
                                        <label for="message" class="form-label">输入要朗读的文本</label>
                                        <textarea class="form-control" id="message" rows="6" placeholder="输入您想让数字人朗读的文字..."></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-volume-up"></i> 朗读文本
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Made with ❤️ by Marstaos | Frontend & Performance Optimization</p>
        </div>
    </div>

    <!-- 隐藏的会话ID -->
    <input type="hidden" id="sessionid" value="0">


    <script src="client.js"></script>
    <script src="srs.sdk.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#video-size-slider').on('input', function() {
                const value = $(this).val();
                $('#video-size-value').text(value + '%');
                $('#video').css('width', value + '%');
            });
            function updateConnectionStatus(status) {
                const statusIndicator = $('#connection-status');
                const statusText = $('#status-text');
                
                statusIndicator.removeClass('status-connected status-disconnected status-connecting');
                
                switch(status) {
                    case 'connected':
                        statusIndicator.addClass('status-connected');
                        statusText.text('已连接');
                        break;
                    case 'connecting':
                        statusIndicator.addClass('status-connecting');
                        statusText.text('连接中...');
                        break;
                    case 'disconnected':
                    default:
                        statusIndicator.addClass('status-disconnected');
                        statusText.text('未连接');
                        break;
                }
            }

            // 添加聊天消息
            function addChatMessage(message, type = 'user') {
                const messagesContainer = $('#chat-messages');
                const messageClass = type === 'user' ? 'user-message' : 'system-message';
                const sender = type === 'user' ? '您' : '数字人';
                
                const messageElement = $(`
                    <div class="asr-text ${messageClass}">
                        ${sender}: ${message}
                    </div>
                `);
                
                messagesContainer.append(messageElement);
                messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
            }

            // 开始/停止按钮
            $('#start').click(function() {
                updateConnectionStatus('connecting');
                start();
                $(this).hide();
                $('#stop').show();
                
                // 添加定时器检查视频流是否已加载
                let connectionCheckTimer = setInterval(function() {
                    const video = document.getElementById('video');
                    // 检查视频是否有数据
                    if (video.readyState >= 3 && video.videoWidth > 0) {
                        updateConnectionStatus('connected');
                        clearInterval(connectionCheckTimer);
                    }
                }, 2000); // 每2秒检查一次
                
                // 60秒后如果还是连接中状态，就停止检查
                setTimeout(function() {
                    if (connectionCheckTimer) {
                        clearInterval(connectionCheckTimer);
                    }
                }, 60000);
            });

            $('#stop').click(function() {
                stop();
                $(this).hide();
                $('#start').show();
                updateConnectionStatus('disconnected');
            });

            // 录制功能
            $('#btn_start_record').click(function() {
                console.log('Starting recording...');
                fetch('/record', {
                    body: JSON.stringify({
                        type: 'start_record',
                        sessionid: parseInt(document.getElementById('sessionid').value),
                    }),
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    method: 'POST'
                }).then(function(response) {
                    if (response.ok) {
                        console.log('Recording started.');
                        $('#btn_start_record').prop('disabled', true);
                        $('#btn_stop_record').prop('disabled', false);
                        $('#recording-indicator').addClass('active');
                    } else {
                        console.error('Failed to start recording.');
                    }
                }).catch(function(error) {
                    console.error('Error:', error);
                });
            });

            $('#btn_stop_record').click(function() {
                console.log('Stopping recording...');
                fetch('/record', {
                    body: JSON.stringify({
                        type: 'end_record',
                        sessionid: parseInt(document.getElementById('sessionid').value),
                    }),
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    method: 'POST'
                }).then(function(response) {
                    if (response.ok) {
                        console.log('Recording stopped.');
                        $('#btn_start_record').prop('disabled', false);
                        $('#btn_stop_record').prop('disabled', true);
                        $('#recording-indicator').removeClass('active');
                    } else {
                        console.error('Failed to stop recording.');
                    }
                }).catch(function(error) {
                    console.error('Error:', error);
                });
            });

            $('#echo-form').on('submit', function(e) {
                e.preventDefault();
                var message = $('#message').val();
                if (!message.trim()) return;
                
                console.log('Sending echo message:', message);
                
                fetch('/human', {
                    body: JSON.stringify({
                        text: message,
                        type: 'echo',
                        interrupt: true,
                        sessionid: parseInt(document.getElementById('sessionid').value),
                    }),
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    method: 'POST'
                });
                
                $('#message').val('');
                addChatMessage(`已发送朗读请求: "${message}"`, 'system');
            });

            // 聊天模式表单提交
            $('#chat-form').on('submit', function(e) {
                e.preventDefault();
                var message = $('#chat-message').val();
                if (!message.trim()) return;
                
                console.log('Sending chat message:', message);
                
                fetch('/human', {
                    body: JSON.stringify({
                        text: message,
                        type: 'chat',
                        interrupt: true,
                        sessionid: parseInt(document.getElementById('sessionid').value),
                    }),
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    method: 'POST'
                });
                
                addChatMessage(message, 'user');
                $('#chat-message').val('');
            });

            // 语音交互功能
            let mediaRecorder;
            let audioChunks = [];
            let isRecording = false;
            let recognition;
            let currentVoiceMode = 'push-to-talk'; // 'push-to-talk' 或 'continuous'
            let isContinuousMode = false;
            let continuousRecognition = null;

            // 语音转文字实时显示相关变量
            let currentInterimText = '';
            let currentFinalText = '';
            let speechDisplayTimer = null;
            let isShowingSpeechPreview = false;
            
            // 检查浏览器是否支持语音识别
            const isSpeechRecognitionSupported = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;

            // 实时语音转文字显示函数
            function updateSpeechDisplay(interimText, finalText, mode = 'push-to-talk') {
                const chatMessage = $('#chat-message');
                const speechPreview = $('#speech-preview');
                const speechPreviewText = $('#speech-preview-text');

                // 更新全局状态
                currentInterimText = interimText;
                currentFinalText = finalText;

                // 只在文本输入框显示最终结果
                if (finalText) {
                    chatMessage.val(finalText);
                } else if (mode === 'push-to-talk' && interimText) {
                    // 按住说话模式下，也可以显示临时结果供用户预览
                    chatMessage.val(interimText);
                }

                // 更新预览区域显示详细的识别状态
                if (interimText || finalText) {
                    let previewText = '';
                    if (finalText) {
                        previewText += `✅ 识别完成: "${finalText}"`;
                    }
                    if (interimText) {
                        if (finalText) previewText += ' | ';
                        previewText += `🎤 识别中: "${interimText}"`;
                    }
                    speechPreviewText.text(previewText);
                    speechPreview.addClass('active');
                    isShowingSpeechPreview = true;
                } else {
                    speechPreviewText.text('等待语音输入...');
                    speechPreview.removeClass('active');
                    isShowingSpeechPreview = false;
                }

                // 自动滚动到文本末尾
                if (chatMessage.val()) {
                    chatMessage[0].scrollTop = chatMessage[0].scrollHeight;
                }
            }

            // 清除语音显示
            function clearSpeechDisplay() {
                currentInterimText = '';
                currentFinalText = '';
                $('#speech-preview').removeClass('active');
                $('#speech-preview-text').text('等待语音输入...');
                isShowingSpeechPreview = false;

                // 清除定时器
                if (speechDisplayTimer) {
                    clearTimeout(speechDisplayTimer);
                    speechDisplayTimer = null;
                }
            }

            // 开始语音识别显示
            function startSpeechDisplay(mode = 'push-to-talk') {
                clearSpeechDisplay();
                $('#speech-preview').addClass('active');
                $('#speech-preview-text').text('🎤 正在启动语音识别...');
                isShowingSpeechPreview = true;
            }

            // 结束语音识别显示
            function endSpeechDisplay(keepFinalText = true) {
                if (keepFinalText && currentFinalText) {
                    // 保留最终文本，更新预览状态
                    $('#speech-preview-text').text(`✅ 识别完成: "${currentFinalText}"`);

                    // 3秒后清除预览
                    speechDisplayTimer = setTimeout(() => {
                        $('#speech-preview').removeClass('active');
                        isShowingSpeechPreview = false;
                    }, 3000);
                } else {
                    // 立即清除所有显示
                    clearSpeechDisplay();
                }
            }

            // 初始化语音识别（按住说话模式）
            if (isSpeechRecognitionSupported) {
                recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'zh-CN';

                recognition.onstart = function() {
                    console.log('按住说话模式：语音识别已启动');
                    startSpeechDisplay('push-to-talk');
                };

                recognition.onresult = function(event) {
                    let interimTranscript = '';
                    let finalTranscript = '';

                    // 收集所有结果
                    for (let i = 0; i < event.results.length; ++i) {
                        if (event.results[i].isFinal) {
                            finalTranscript += event.results[i][0].transcript;
                        } else {
                            interimTranscript += event.results[i][0].transcript;
                        }
                    }

                    // 实时更新显示
                    updateSpeechDisplay(interimTranscript, finalTranscript, 'push-to-talk');

                    console.log('按住说话识别结果:', {
                        interim: interimTranscript,
                        final: finalTranscript
                    });
                };

                recognition.onerror = function(event) {
                    console.error('按住说话语音识别错误:', event.error);
                    endSpeechDisplay(false);

                    // 显示错误提示
                    $('#speech-preview-text').text(`❌ 识别错误: ${event.error}`);
                    $('#speech-preview').addClass('active');
                    setTimeout(() => {
                        $('#speech-preview').removeClass('active');
                    }, 3000);
                };

                recognition.onend = function() {
                    console.log('按住说话模式：语音识别已结束');
                    endSpeechDisplay(true);
                };

                // 初始化持续对话模式的语音识别
                continuousRecognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                continuousRecognition.continuous = true;
                continuousRecognition.interimResults = true;
                continuousRecognition.lang = 'zh-CN';

                continuousRecognition.onstart = function() {
                    console.log('持续对话模式：语音识别已启动');
                    if (isContinuousMode) {
                        startSpeechDisplay('continuous');
                    }
                };

                continuousRecognition.onresult = function(event) {
                    if (!isContinuousMode) return;

                    let interimTranscript = '';
                    let finalTranscript = '';

                    // 收集所有结果
                    for (let i = 0; i < event.results.length; ++i) {
                        if (event.results[i].isFinal) {
                            finalTranscript += event.results[i][0].transcript;
                        } else {
                            interimTranscript += event.results[i][0].transcript;
                        }
                    }

                    // 实时更新显示
                    updateSpeechDisplay(interimTranscript, finalTranscript, 'continuous');

                    console.log('持续对话识别结果:', {
                        interim: interimTranscript,
                        final: finalTranscript
                    });

                    // 如果有最终结果，发送消息
                    if (finalTranscript.trim()) {
                        // 显示发送状态
                        $('#speech-preview-text').text(`📤 正在发送: "${finalTranscript.trim()}"`);

                        // 发送消息
                        sendChatMessage(finalTranscript.trim());

                        // 清除显示，准备下一次识别
                        setTimeout(() => {
                            if (isContinuousMode) {
                                clearSpeechDisplay();
                                $('#speech-preview').addClass('active');
                                $('#speech-preview-text').text('🎤 等待下一次语音输入...');
                            }
                        }, 1000);
                    }
                };

                continuousRecognition.onerror = function(event) {
                    console.error('持续语音识别错误:', event.error);
                    if (isContinuousMode) {
                        updateVoiceStatus('识别错误，请重新开始', 'error');
                        endSpeechDisplay(false);

                        // 显示错误提示
                        $('#speech-preview-text').text(`❌ 识别错误: ${event.error}`);
                        $('#speech-preview').addClass('active');
                    }
                };

                continuousRecognition.onend = function() {
                    console.log('持续对话模式：语音识别已结束');
                    if (isContinuousMode) {
                        // 如果还在持续模式，重新启动识别
                        setTimeout(() => {
                            if (isContinuousMode) {
                                try {
                                    continuousRecognition.start();
                                } catch (e) {
                                    console.log('重启语音识别:', e);
                                }
                            }
                        }, 100);
                    } else {
                        endSpeechDisplay(false);
                    }
                };
            }

            // 语音模式切换
            $('.voice-mode-tab').click(function() {
                const mode = $(this).data('mode');
                switchVoiceMode(mode);
            });

            // 持续对话控制按钮
            $('#continuous-start-btn').click(function() {
                startContinuousMode();
            });

            $('#continuous-stop-btn').click(function() {
                stopContinuousMode();
            });

            // 按住说话按钮事件
            $('#voice-record-btn').on('mousedown touchstart', function(e) {
                e.preventDefault();
                if (currentVoiceMode === 'push-to-talk') {
                    startRecording();
                }
            }).on('mouseup mouseleave touchend', function() {
                if (isRecording && currentVoiceMode === 'push-to-talk') {
                    stopRecording();
                }
            });
            
            // 开始录音
            function startRecording() {
                if (isRecording) return;

                console.log('开始按住说话录音');

                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(function(stream) {
                        audioChunks = [];
                        mediaRecorder = new MediaRecorder(stream);

                        mediaRecorder.ondataavailable = function(e) {
                            if (e.data.size > 0) {
                                audioChunks.push(e.data);
                            }
                        };

                        mediaRecorder.start();
                        isRecording = true;

                        // 视觉反馈
                        $('#voice-record-btn').addClass('recording-pulse');
                        $('#voice-record-btn').css('background-color', '#dc3545');

                        // 清除之前的文本
                        $('#chat-message').val('');
                        clearSpeechDisplay();

                        // 启动语音识别
                        if (recognition) {
                            try {
                                recognition.start();
                            } catch (e) {
                                console.log('启动语音识别失败:', e);
                            }
                        }
                    })
                    .catch(function(error) {
                        console.error('无法访问麦克风:', error);
                        alert('无法访问麦克风，请检查浏览器权限设置。');
                        endSpeechDisplay(false);
                    });
            }

            function stopRecording() {
                if (!isRecording) return;

                console.log('停止按住说话录音');

                mediaRecorder.stop();
                isRecording = false;

                // 停止所有音轨
                mediaRecorder.stream.getTracks().forEach(track => track.stop());

                // 视觉反馈恢复
                $('#voice-record-btn').removeClass('recording-pulse');
                $('#voice-record-btn').css('background-color', '');

                // 停止语音识别
                if (recognition) {
                    try {
                        recognition.stop();
                    } catch (e) {
                        console.log('停止语音识别失败:', e);
                    }
                }

                // 等待语音识别完成，然后发送文本
                setTimeout(function() {
                    const recognizedText = $('#chat-message').val().trim();
                    if (recognizedText) {
                        console.log('发送识别的文本:', recognizedText);

                        // 更新预览显示
                        $('#speech-preview-text').text(`📤 正在发送: "${recognizedText}"`);

                        // 发送消息
                        sendChatMessage(recognizedText);

                        // 清除输入框和显示
                        $('#chat-message').val('');
                        setTimeout(() => {
                            clearSpeechDisplay();
                        }, 2000);
                    } else {
                        console.log('没有识别到文本');
                        endSpeechDisplay(false);
                    }
                }, 500);
            }

            // 发送聊天消息的统一函数
            function sendChatMessage(text) {
                if (!text.trim()) return;

                fetch('/human', {
                    body: JSON.stringify({
                        text: text,
                        type: 'chat',
                        interrupt: true,
                        sessionid: parseInt(document.getElementById('sessionid').value),
                    }),
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    method: 'POST'
                });

                addChatMessage(text, 'user');
            }

            // 切换语音模式
            function switchVoiceMode(mode) {
                console.log('切换语音模式到:', mode);

                // 停止当前模式
                if (isContinuousMode) {
                    stopContinuousMode();
                }
                if (isRecording) {
                    stopRecording();
                }

                // 清除语音显示
                clearSpeechDisplay();
                $('#chat-message').val('');

                currentVoiceMode = mode;

                // 更新UI
                $('.voice-mode-tab').removeClass('active');
                $(`.voice-mode-tab[data-mode="${mode}"]`).addClass('active');

                if (mode === 'push-to-talk') {
                    $('#push-to-talk-controls').addClass('active');
                    $('#continuous-controls').removeClass('active');
                } else {
                    $('#push-to-talk-controls').removeClass('active');
                    $('#continuous-controls').addClass('active');
                }

                console.log('语音模式切换完成:', mode);
            }

            // 开始持续对话模式
            function startContinuousMode() {
                if (!isSpeechRecognitionSupported) {
                    alert('您的浏览器不支持语音识别功能');
                    return;
                }

                console.log('开始持续对话模式');

                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(function(stream) {
                        isContinuousMode = true;

                        // 更新UI
                        $('#continuous-start-btn').hide();
                        $('#continuous-stop-btn').show();
                        updateVoiceStatus('正在监听...', 'recording');

                        // 清除之前的显示
                        $('#chat-message').val('');
                        clearSpeechDisplay();

                        // 启动语音识别
                        if (continuousRecognition) {
                            try {
                                continuousRecognition.start();
                            } catch (e) {
                                console.log('启动持续语音识别失败:', e);
                                updateVoiceStatus('启动识别失败', 'error');
                            }
                        }

                        // 保存音频流引用以便后续停止
                        window.continuousStream = stream;

                        console.log('持续对话模式已启动');
                    })
                    .catch(function(error) {
                        console.error('无法访问麦克风:', error);
                        alert('无法访问麦克风，请检查浏览器权限设置。');
                        updateVoiceStatus('麦克风访问失败', 'error');
                        endSpeechDisplay(false);
                    });
            }

            // 停止持续对话模式
            function stopContinuousMode() {
                console.log('停止持续对话模式');

                isContinuousMode = false;

                // 停止语音识别
                if (continuousRecognition) {
                    try {
                        continuousRecognition.stop();
                    } catch (e) {
                        console.log('停止持续语音识别失败:', e);
                    }
                }

                // 停止音频流
                if (window.continuousStream) {
                    window.continuousStream.getTracks().forEach(track => track.stop());
                    window.continuousStream = null;
                }

                // 清除语音显示
                clearSpeechDisplay();
                $('#chat-message').val('');

                // 更新UI
                $('#continuous-start-btn').show();
                $('#continuous-stop-btn').hide();
                updateVoiceStatus('点击开始持续对话', 'idle');

                console.log('持续对话模式已停止');
            }

            // 更新语音状态显示
            function updateVoiceStatus(text, status) {
                const statusElement = $('#voice-status');
                statusElement.text(text);
                statusElement.removeClass('recording idle error');
                statusElement.addClass(status);
            }

            // WebRTC 相关功能
            if (typeof window.onWebRTCConnected === 'function') {
                const originalOnConnected = window.onWebRTCConnected;
                window.onWebRTCConnected = function() {
                    updateConnectionStatus('connected');
                    if (originalOnConnected) originalOnConnected();
                };
            } else {
                window.onWebRTCConnected = function() {
                    updateConnectionStatus('connected');
                };
            }

            // 当连接断开时更新状态
            if (typeof window.onWebRTCDisconnected === 'function') {
                const originalOnDisconnected = window.onWebRTCDisconnected;
                window.onWebRTCDisconnected = function() {
                    updateConnectionStatus('disconnected');
                    if (originalOnDisconnected) originalOnDisconnected();
                };
            } else {
                window.onWebRTCDisconnected = function() {
                    updateConnectionStatus('disconnected');
                };
            }

            // SRS WebRTC播放功能
            var sdk = null; // 全局处理器，用于在重新发布时进行清理

            function startPlay() {
                // 关闭之前的连接
                if (sdk) {
                    sdk.close();
                }

                sdk = new SrsRtcWhipWhepAsync();
                $('#video').prop('srcObject', sdk.stream);

                var host = window.location.hostname;
                var url = "http://" + host + ":1985/rtc/v1/whep/?app=live&stream=livestream";

                sdk.play(url).then(function(session) {
                    console.log('WebRTC播放已启动，会话ID:', session.sessionid);
                }).catch(function(reason) {
                    sdk.close();
                    console.error('WebRTC播放失败:', reason);
                });
            }

            // 页面卸载时清理资源
            $(window).on('beforeunload', function() {
                console.log('页面卸载，清理语音资源');

                if (isContinuousMode) {
                    stopContinuousMode();
                }
                if (isRecording) {
                    stopRecording();
                }

                // 清理语音显示
                clearSpeechDisplay();

                // 停止所有语音识别
                if (recognition) {
                    try {
                        recognition.stop();
                    } catch (e) {}
                }
                if (continuousRecognition) {
                    try {
                        continuousRecognition.stop();
                    } catch (e) {}
                }
            });
        });
    </script>
</body>
</html>