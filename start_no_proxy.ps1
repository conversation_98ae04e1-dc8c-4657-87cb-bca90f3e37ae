# LiveTalking 无代理启动脚本
# 专门处理华为代理环境

Write-Host "LiveTalking 无代理启动脚本" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

# 显示当前代理设置
Write-Host "检测到的系统代理设置:" -ForegroundColor Yellow
Write-Host "  HTTP_PROXY: $env:HTTP_PROXY" -ForegroundColor Cyan
Write-Host "  HTTPS_PROXY: $env:HTTPS_PROXY" -ForegroundColor Cyan

# 强制清除当前会话的所有代理设置
Write-Host "清除当前会话的代理设置..." -ForegroundColor Yellow
$env:HTTP_PROXY = $null
$env:HTTPS_PROXY = $null
$env:http_proxy = $null
$env:https_proxy = $null
$env:NO_PROXY = "127.0.0.1,localhost,*.local,10.*,192.168.*"
$env:no_proxy = "127.0.0.1,localhost,*.local,10.*,192.168.*"

# 设置 Python 相关环境变量
$env:PYTHONIOENCODING = "utf-8"
$env:REQUESTS_CA_BUNDLE = ""
$env:CURL_CA_BUNDLE = ""

# 验证清除结果
Write-Host "当前会话代理设置已清除:" -ForegroundColor Green
Write-Host "  HTTP_PROXY: '$env:HTTP_PROXY'" -ForegroundColor Cyan
Write-Host "  HTTPS_PROXY: '$env:HTTPS_PROXY'" -ForegroundColor Cyan
Write-Host "  NO_PROXY: '$env:NO_PROXY'" -ForegroundColor Cyan

# 激活 conda 环境
Write-Host "激活 conda 环境..." -ForegroundColor Yellow
try {
    conda activate nerfstream
    Write-Host "✅ 已激活 nerfstream 环境" -ForegroundColor Green
} catch {
    Write-Host "❌ 无法激活 nerfstream 环境: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动激活环境: conda activate nerfstream" -ForegroundColor Yellow
}

# 测试本地连接
Write-Host "测试本地 GPT-SoVITS 服务连接..." -ForegroundColor Yellow
try {
    # 使用 .NET WebClient 绕过代理
    $webClient = New-Object System.Net.WebClient
    $webClient.Proxy = $null  # 禁用代理
    $response = $webClient.DownloadString("http://127.0.0.1:9880/")
    Write-Host "✅ GPT-SoVITS 服务连接成功" -ForegroundColor Green
} catch {
    Write-Host "❌ GPT-SoVITS 服务连接失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请确保 GPT-SoVITS 在 http://127.0.0.1:9880 运行" -ForegroundColor Yellow
    
    $continue = Read-Host "是否继续启动应用？(y/n)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# 检查参考音频文件
$refFile = "D:\ai\Liv2\ref.wav"
Write-Host "检查参考音频文件: $refFile" -ForegroundColor Yellow
if (Test-Path $refFile) {
    Write-Host "✅ 参考音频文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ 参考音频文件不存在: $refFile" -ForegroundColor Red
    Write-Host "请确保参考音频文件路径正确" -ForegroundColor Yellow
    
    $continue = Read-Host "是否继续启动应用？(y/n)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# 启动应用
Write-Host "启动 LiveTalking 应用..." -ForegroundColor Green
Write-Host "配置参数:" -ForegroundColor Cyan
Write-Host "  - 传输方式: webrtc" -ForegroundColor Cyan
Write-Host "  - 模型: wav2lip" -ForegroundColor Cyan
Write-Host "  - 头像: wav2lip256_avatar1" -ForegroundColor Cyan
Write-Host "  - TTS: gpt-sovits" -ForegroundColor Cyan
Write-Host "  - TTS 服务器: http://127.0.0.1:9880" -ForegroundColor Cyan
Write-Host "  - 参考音频: $refFile" -ForegroundColor Cyan
Write-Host "  - 参考文本: 轻量化技术的连续覆盖网络，面向重庆复杂的道路交通" -ForegroundColor Cyan

Write-Host ""
Write-Host "正在启动应用，请稍候..." -ForegroundColor Green
Write-Host "如果出现代理错误，请检查 GPT-SoVITS 服务是否正常运行" -ForegroundColor Yellow

# 启动命令
python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "D:\ai\Liv2\ref.wav" --REF_TEXT "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"
