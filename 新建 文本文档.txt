# 密码特殊字符编码：`.` → `%2E`
$env:HTTP_PROXY = "http://d84190609:<EMAIL>:8080"
$env:HTTPS_PROXY = "http://d84190609:<EMAIL>:8080"
conda create -n nerfstream python=3.10
conda activate nerfstream
#如果cuda版本不为11.3(运行nvidia-smi确认版本)，根据<https://pytorch.org/get-started/previous-versions/>安装对应版本的pytorch 
conda install pytorch==1.12.1 torchvision==0.13.1 cudatoolkit=11.3 -c pytorch

pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --index-url https://download.pytorch.org/whl/cu124

pip install -r requirements.txt\
  -i https://pypi.tuna.tsinghua.edu.cn/simple \
  --trusted-host pypi.tuna.tsinghua.edu.cn \
  --retries 10 --timeout 120

#如果不训练ernerf模型，不需要安装下面的库
pip install "git+https://github.com/facebookresearch/pytorch3d.git"
pip install tensorflow-gpu==2.8.0
pip install --upgrade "protobuf<=3.20.1"

python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1

 api_key=os.getenv("sk-2a7b337ad87846e284c123b0b0ba825f"),
export DASHSCOPE_API_KEY=<your_api_key>

cd F:\BaiduNetdiskDownload\LiveTalkin\Liv2
conda activate nerfstream
python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar2
set DASHSCOPE_API_KEY=sk-a817462b6fa246128ff0b412bb22efd8
python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1
python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar2 --tts cosyvoice --TTS_SERVER http://127.0.0.1:50000 --REF_FILE ref.wav --REF_TEXT 重庆移动率先建成全国首个全域支持5G

python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar2  F:/BaiduNetdiskDownload/LiveTalkin/Liv2/CosyVoice/ref.wav --REF_TEXT 重庆移动率先建成全国首个全域支持5G

使用CosyVoice
conda activate cosyvoice
cd CosyVoice/runtime/python/fastapi/
python server.py --model_dir ../../../pretrained_models/CosyVoice2-0.5B

cd F:\BaiduNetdiskDownload\LiveTalkin\Liv2
conda activate nerfstream
python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar2 --tts cosyvoice --TTS_SERVER http://127.0.0.1:50000 --REF_FILE F:/BaiduNetdiskDownload/LiveTalkin/Liv2/CosyVoice/ref.wav --REF_TEXT 重庆移动率先建成全国首个全域支持5G

https://livetalking-doc.readthedocs.io/zh-cn/latest/tts/gptsovits.html#id1
GPT-SoVITS
cd GPT-SoVITS
conda activate sovits
python api_v2.py


cd F:\BaiduNetdiskDownload\LiveTalkin\Liv2
cd D:\ai\LiveTalking
conda activate nerfstream
python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar2 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE /mnt/f/BaiduNetdiskDownload/LiveTalkin/Liv2/ref.wav  --REF_TEXT 轻量化技术的连续覆盖网络，面向重庆复杂的道路交通。

python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar2 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE  F:\BaiduNetdiskDownload\LiveTalkin\Liv2\ref.wav  --REF_TEXT 轻量化技术的连续覆盖网络，面向重庆复杂的道路交通

python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "D:\ai\Liv2\ref.wav" --REF_TEXT "轻量化技术的连续覆盖网络，面向重庆复杂的道路交通"

cd F:\BaiduNetdiskDownload\LiveTalkin\Liv2
conda activate nerfstream
python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar2 --tts IndexTTS --TTS_SERVER http://127.0.0.1:7860  --REF_FILE  test1

python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar2 --tts IndexTTS --TTS_SERVER http://127.0.0.1:7860 --REF_FILE test1